# 積分回饋分配技術實現文檔

## 概述

本文檔詳細說明 `OrderHelper::do_pointback()` 方法的技術實現，包括核心邏輯、關鍵方法和代碼結構。

## 核心架構

### 主要類別
- **OrderHelper**: 主要業務邏輯處理
- **BonusHelper**: 分潤計算和用戶數據管理
- **MemberInstance**: 用戶數據查詢

### 數據流程
```
訂單數據 → 商品處理 → 分配決策 → 各類分潤計算 → 積分分配 → 記錄更新
```

## 方法結構分析

### do_pointback() 主流程

#### 1. 初始化階段 (行 1950-1990)
```php
// 初始化BonusHelper和相關實例
$BonusHelper = new BonusHelper();
$MemberInstance = new MemberInstance();

// 載入基礎數據
$BonusHelper->init_bonus_models();
$BonusHelper->init_center_levels();
$BonusHelper->init_partner_levels();
$BonusHelper->init_vip_types();
```

#### 2. 訂單循環處理 (行 1990-2500)
```php
foreach ($orderforms as $orderform) {
    // 訂單相關人員初始化
    // 商品循環處理
    // 分配決策邏輯
    // 各類分潤計算
}
```

#### 3. 積分分配執行 (行 2500+)
```php
// 執行所有積分分配
$BonusHelper->send_by_cal('線上消費回饋');
```

### 關鍵決策邏輯

#### 分配模式決策 (行 2080-2120)
```php
// 投資商品邏輯
if ($product['product_cate'] == 1) {
    if ($orderform['registration_from'] == 1) {
        // 直推來源 → 推薦者個人
        $recommend_mode = 'personal';
    } else {
        // 廣告來源 → 合夥人共享
        $recommend_mode = 'partner_share';
    }
}

// 消費商品邏輯
else {
    $recommender_vip_level = $BonusHelper->get('user_cal')[$recommender_id]['data']['vip_type'] ?? 0;
    $is_recommender_partner = in_array($recommender_id, $active_partner_ids);
    
    if (($recommender_vip_level >= 2 && $newVipLevel > 0) || 
        (!$is_recommender_partner && $newVipLevel == 0)) {
        $recommend_mode = 'personal';
    } else {
        $recommend_mode = 'partner_share';
    }
}
```

### 推廣獎勵分配

#### 個人分配模式 (行 2125-2160)
```php
// 燒傷限制計算
$recommender_burn_cv = $BonusHelper->get('user_cal')[$recommender_id]['data']['burn_cv'] ?? 0;
$effective_cv = min($count_cv, $recommender_burn_cv);

// 推廣獎勵計算
$recommend_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 1, $effective_cv);
$BonusHelper->add_available_cv($recommender_id, $recommend_cv);
```

#### 合夥人共享模式 (行 2170-2270)
```php
// 課程分段計算
$course_segments = self::calculateCourseSegments($product, $newVipLevel, $recommend_cv, $BonusHelper);

foreach ($course_segments as $segment) {
    $required_level = $segment['required_level'];
    $segment_cv = $segment['cv'];
    
    // 篩選符合條件的合夥人
    $qualified_partners = [];
    foreach ($active_partners as $partner_id => $weight) {
        $partner_course_level = $user_cal_data[$partner_id]['data']['vip_type_course'] ?? 0;
        if ($partner_course_level >= $required_level) {
            $qualified_partners[$partner_id] = $weight;
        }
    }
    
    // 按權重分配
    if ($qualified_total_weight > 0) {
        foreach ($qualified_partners as $partner_id => $weight) {
            $partner_cv = ($weight / $qualified_total_weight) * $segment_cv;
            $BonusHelper->add_available_cv($partner_id, $partner_cv);
        }
    }
}
```

### 課程分段計算邏輯

#### calculateCourseSegments() 方法 (行 2521-2700)
```php
private static function calculateCourseSegments($product, $newVipLevel, $totalCV, $BonusHelper)
{
    $segments = [];
    $product_cv = $product['price_cv'] ?? 0;
    
    // 獲取各課程的burn_cv值
    $rendu_burn_cv = $BonusHelper->arr_vip_types[2]['burn_cv'] ?? 0;
    $zhongmai_burn_cv = $BonusHelper->arr_vip_types[3]['burn_cv'] ?? 0;
    // ... 其他課程burn_cv
    
    switch ($newVipLevel) {
        case 2: // 任督課程
            $segments[] = [
                'cv' => $totalCV,
                'required_level' => 2,
                'description' => '任督CV部分'
            ];
            break;
            
        case 3: // 中脈課程
            // 任督部分
            $rendu_ratio = min($rendu_burn_cv, $product_cv) / $product_cv;
            $segments[] = [
                'cv' => $totalCV * $rendu_ratio,
                'required_level' => 2,
                'description' => '任督CV部分'
            ];
            
            // 中脈差額部分
            if ($product_cv > $rendu_burn_cv) {
                $zhongmai_diff_ratio = min($zhongmai_burn_cv - $rendu_burn_cv, $product_cv - $rendu_burn_cv) / $product_cv;
                $segments[] = [
                    'cv' => $totalCV * $zhongmai_diff_ratio,
                    'required_level' => 3,
                    'description' => '中脈CV差額部分'
                ];
            }
            break;
            
        // ... 其他課程類型
    }
    
    return $segments;
}
```

### 合夥平級獎勵分配

#### distributePartnerBonusToDirectUplines() 方法 (行 2660-2730)
```php
private static function distributePartnerBonusToDirectUplines($partners_with_rewards, $share_cv, $BonusHelper, $orderform)
{
    $upline_weights = [];
    
    // 收集上線合夥人權重
    foreach ($partners_with_rewards as $partner_id) {
        $upline_user_id = $BonusHelper->get('user_cal')[$partner_id]['data']['upline_user'] ?? 0;
        
        if ($upline_user_id > 0) {
            // 避免重複初始化已有數據
            if (!isset($BonusHelper->get('user_cal')[$upline_user_id])) {
                $BonusHelper->init_user_set($upline_user_id);
            }
            
            $upline_partner_level = $BonusHelper->get('user_cal')[$upline_user_id]['data']['partner_level_id'] ?? 0;
            if ($upline_partner_level > 0) {
                $weight = $BonusHelper->arr_partner_levels[$upline_partner_level]['partner_bonus_ratio'] ?? 0;
                $upline_weights[$upline_user_id] = ($upline_weights[$upline_user_id] ?? 0) + ($weight / 100);
            }
        }
    }
    
    // 計算並分配合夥平級獎勵
    $total_bonus_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 2, $share_cv);
    $total_weight = array_sum($upline_weights);
    
    if ($total_weight > 0) {
        foreach ($upline_weights as $upline_id => $weight) {
            $upline_cv = ($weight / $total_weight) * $total_bonus_cv;
            $BonusHelper->add_available_cv($upline_id, $upline_cv);
        }
    }
}
```

### 中心分潤計算

#### 中心分潤邏輯 (行 2376-2408)
```php
// 獲取中心分潤總CV
$available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 9, $share_cv);

// 獲取中心等級
$user_id_center_level = $BonusHelper->get('user_cal')[$user_id_center]['data']['center_level_id'] ?? 0;
$user_id_center_upper_level = $BonusHelper->get('user_cal')[$user_id_center_upper]['data']['center_level_id'] ?? 0;

// 計算分潤比率
$center_level_diff_result = $BonusHelper->count_center_level_diff($user_id_center_level, $user_id_center_upper_level);
$level_ratio = $center_level_diff_result['level_weight'];

// 分配CV
$divided_num_lower = $available_cv * $level_ratio['center_lower'] / 100;
$divided_num_upper = $available_cv * $level_ratio['center_upper'] / 100;

$BonusHelper->add_available_cv_center($user_id_center, $divided_num_lower, $product['bonus_model_id']);
$BonusHelper->add_available_cv_center($user_id_center_upper, $divided_num_upper, $product['bonus_model_id']);
```

## 關鍵BonusHelper方法

### count_available_cv() - 分潤CV計算
```php
public function count_available_cv($bonus_model_id, $count_type, $count_cv)
{
    $count_type_column = $this->count_type_columns[$count_type] ?? '';
    $ratio = $this->arr_bonus_models[$bonus_model_id][$count_type_column] ?? 0;
    return (float)($count_cv * $ratio / 100);
}
```

### count_center_level_diff() - 中心等級差額計算
```php
public function count_center_level_diff(int $center_lower, int $center_upper)
{
    // 本層中心分潤比率
    $level_ratio['center_lower'] = $this->arr_center_levels[$center_lower]['cv_ratio'] ?? 0;
    
    // 上層中心分潤比率（差額）
    $upper_cv_ratio = $this->arr_center_levels[$center_upper]['cv_ratio'] ?? 0;
    $lower_cv_ratio = $level_ratio['center_lower'];
    
    if ($upper_cv_ratio > $lower_cv_ratio) {
        $level_ratio['center_upper'] = $upper_cv_ratio - $lower_cv_ratio;
    } else {
        $level_ratio['center_upper'] = 0;
    }
    
    return ['level_weight' => $level_ratio];
}
```

### add_available_cv() - 積分累加
```php
public function add_available_cv($user_id, $cv)
{
    if (!isset($this->user_cal[$user_id])) {
        $this->init_user_set($user_id);
    }
    $this->user_cal[$user_id]['available_cv'] += $cv;
}
```

## 數據結構

### user_cal 結構
```php
$this->user_cal[$user_id] = [
    'data' => [
        'id' => $user_id,
        'vip_type' => 0,
        'vip_type_course' => 0,
        'partner_level_id' => 0,
        'center_level_id' => 0,
        'upline_user' => 0,
        // ... 其他用戶數據
    ],
    'available_cv' => 0.0,
    'available_cv_center' => 0.0,
    // ... 其他統計數據
];
```

### 回饋模組結構
```php
$this->arr_bonus_models[$bonus_model_id] = [
    'normal_recommend' => 70,      // 推廣獎勵比率
    'normal_partner' => 10,        // 合夥平級獎勵比率
    'normal_marketing_dept' => 5,  // 行政部門比率
    'normal_sales_dept' => 5,      // 業務部門比率
    'normal_center' => 20,         // 中心分潤比率
    // ... 其他角色比率
];
```

## 重要注意事項

### 1. 用戶數據初始化
- 使用 `init_user_set()` 初始化用戶數據
- 避免重複初始化導致數據丟失
- 使用 `get('user_cal')` 方法安全訪問數據

### 2. 權重計算
- 合夥人權重來自 `partner_levels` 表的相關比率
- 權重計算需要除以100轉換為小數
- 總權重為0時不進行分配

### 3. 燒傷機制
- 個人推廣獎勵受燒傷CV限制
- 燒傷CV由會員級別的 `burn_cv` 決定
- 合夥人分配時每個合夥人都有燒傷限制

### 4. 課程等級檢查
- 使用 `vip_type_course` 字段判斷課程等級
- 必須 `>=` 要求等級才能參與分配
- 高等級可以參與多個分段

### 5. 錯誤處理
- 檢查數組索引是否存在
- 使用 `??` 運算符提供默認值
- 記錄詳細的分配日誌

## 性能考量

### 1. 數據預載
- 在初始化階段載入所有基礎數據
- 避免在循環中重複查詢數據庫

### 2. 記憶體管理
- 大量用戶數據存儲在 `user_cal` 中
- 考慮分批處理大量訂單

### 3. 日誌記錄
- 使用結構化日誌記錄分配詳情
- 便於問題排查和業務驗證

---

**文檔版本**: 1.0  
**最後更新**: 2025-08-25  
**維護者**: 開發團隊
