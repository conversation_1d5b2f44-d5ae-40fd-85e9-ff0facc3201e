# 中心分潤機制說明

## 概述

本文檔詳細說明系統中的中心分潤機制，包括本層中心和上層中心的分潤邏輯、計算方式和實際案例分析。

## 中心等級結構

### 基本等級設定

| 等級ID | 名稱 | 排序(orders) | CV分潤比率 |
|--------|------|-------------|-----------|
| 1      | 區級 | 1           | 10%       |
| 2      | 市級 | 2           | 15%       |
| 3      | 省級 | 3           | 20%       |

**說明**：
- `orders` 數值越小等級越低
- `cv_ratio` 表示該等級中心可獲得的最大分潤比率
- 省級為目前系統中的最高等級

## 分潤機制原理

### 核心設計原則

1. **階梯式分潤**：上層中心只能獲得比下層中心更高等級的差額分潤
2. **避免重複分配**：確保總分潤不超過最高等級的分潤比率
3. **層級關係**：上層中心必須是下層中心的直接上線用戶
4. **發起人分配**：每個中心的CV會進一步分配給其發起人

### 上層中心查找邏輯

```php
// 上層中心 = 本層中心的直接上線用戶
$user_id_center_upper = $BonusHelper->get('user_cal')[$user_id_center]['data']['upline_user'] ?? 0;
```

**重要**：上層中心不是通過中心等級結構查找，而是通過用戶的上下線關係確定。

## 分潤計算邏輯

### 計算步驟

1. **確定中心等級**
   - 獲取本層中心的等級ID和CV分潤比率
   - 獲取上層中心的等級ID和CV分潤比率

2. **計算分潤比率**
   ```php
   // 本層中心：使用該等級的完整cv_ratio
   $level_ratio['center_lower'] = $this->arr_center_levels[$center_lower]['cv_ratio'];
   
   // 上層中心：只能獲得比下層中心更高的差額
   if ($upper_cv_ratio > $lower_cv_ratio) {
       $level_ratio['center_upper'] = $upper_cv_ratio - $lower_cv_ratio;
   } else {
       $level_ratio['center_upper'] = 0;
   }
   ```

3. **分配CV**
   ```php
   // 本層中心分潤
   $divided_num_lower = $available_cv * $level_ratio['center_lower'] / 100;
   
   // 上層中心分潤
   $divided_num_upper = $available_cv * $level_ratio['center_upper'] / 100;
   ```

### 特殊情況處理

#### 情況1：沒有上層中心
- **條件**：本層中心的 `upline_user` 為 0
- **結果**：上層中心分潤為 0
- **示例**：省級中心通常沒有上層中心

#### 情況2：上層中心等級不高於本層中心
- **條件**：`upper_cv_ratio <= lower_cv_ratio`
- **結果**：上層中心分潤為 0
- **原因**：避免低等級中心獲得不應得的分潤

#### 情況3：上層中心等級高於本層中心
- **條件**：`upper_cv_ratio > lower_cv_ratio`
- **結果**：上層中心獲得差額分潤
- **計算**：`upper_cv_ratio - lower_cv_ratio`

## 實際案例分析

### 案例1：訂單907 - 有本層和上層中心

#### 基本信息
- **訂單編號**：B20250630Q0UHIAHU
- **商品**：中階中脈優惠套餐
- **購買者ID**：1850

#### 中心結構
```
本層中心(1904) → 上層中心(1224)
    市級(15%)  →    省級(20%)
```

#### 分潤計算
```
本層中心分潤比率 = 15%
上層中心分潤比率 = 20% - 15% = 5%
總分潤比率 = 15% + 5% = 20%
```

#### 實際分配結果
- **本層中心(1904)**：78.54 CV (15%)
- **上層中心(1224)**：26.18 CV (5%)
- **總計**：104.72 CV (20%)

#### 驗證
- 比率關係：78.54 ÷ 26.18 ≈ 3:1 = 15%:5% ✅
- 總比率：等於省級的最大分潤比率20% ✅

### 案例2：訂單909 - 只有本層中心

#### 基本信息
- **訂單編號**：B20250630J56O39BF
- **本層中心(1224)**：省級(20%)
- **上層中心**：無（省級為最高等級）

#### 分潤計算
```
本層中心分潤比率 = 20%
上層中心分潤比率 = 0% (沒有上層中心)
總分潤比率 = 20%
```

#### 實際分配結果
- **本層中心(1224)**：104.72 CV (20%)
- **上層中心**：0 CV (0%)

## 技術實現

### 核心方法

#### 1. count_center_level_diff()
```php
/**
 * 依下層中心等級與上層中心等級 回傳CV分潤比率(區分下、上)
 */
public function count_center_level_diff(int $center_lower, int $center_upper)
{
    // 計算本層中心分潤比率
    $level_ratio['center_lower'] = $this->arr_center_levels[$center_lower]['cv_ratio'] ?? 0;
    
    // 計算上層中心分潤比率（差額）
    $upper_cv_ratio = $this->arr_center_levels[$center_upper]['cv_ratio'] ?? 0;
    $lower_cv_ratio = $level_ratio['center_lower'];
    
    if ($upper_cv_ratio > $lower_cv_ratio) {
        $level_ratio['center_upper'] = $upper_cv_ratio - $lower_cv_ratio;
    } else {
        $level_ratio['center_upper'] = 0;
    }
    
    return ['level_weight' => $level_ratio];
}
```

#### 2. 中心分潤分配邏輯
```php
// 獲取中心分潤總CV
$available_cv = $BonusHelper->count_available_cv($product['bonus_model_id'], 9, $share_cv);

// 獲取中心等級
$user_id_center_level = $BonusHelper->get('user_cal')[$user_id_center]['data']['center_level_id'] ?? 0;
$user_id_center_upper_level = $BonusHelper->get('user_cal')[$user_id_center_upper]['data']['center_level_id'] ?? 0;

// 計算分潤比率
$center_level_diff_result = $BonusHelper->count_center_level_diff($user_id_center_level, $user_id_center_upper_level);
$level_ratio = $center_level_diff_result['level_weight'];

// 分配CV
$divided_num_lower = $available_cv * $level_ratio['center_lower'] / 100;
$divided_num_upper = $available_cv * $level_ratio['center_upper'] / 100;

// 執行分配
$BonusHelper->add_available_cv_center($user_id_center, $divided_num_lower, $product['bonus_model_id']);
$BonusHelper->add_available_cv_center($user_id_center_upper, $divided_num_upper, $product['bonus_model_id']);
```

## 常見問題解答

### Q1: 為什麼有些訂單的上層中心分潤為0？

**A1:** 有以下幾種可能：
1. **本層中心已是最高等級**：如省級中心沒有更高等級的上層中心
2. **沒有上層中心**：本層中心的 `upline_user` 為 0
3. **上層中心等級不高於本層中心**：系統設計避免低等級中心獲得不當分潤

### Q2: 中心分潤的總比率是如何確定的？

**A2:** 中心分潤的總比率等於參與分潤的最高等級中心的 `cv_ratio`：
- 只有本層中心：總比率 = 本層中心的 cv_ratio
- 有本層和上層中心：總比率 = 上層中心的 cv_ratio

### Q3: 上層中心是如何確定的？

**A3:** 上層中心通過本層中心的 `upline_user` 字段確定，不是通過中心等級結構查找。這確保了分潤遵循實際的組織架構關係。

## 結論

中心分潤機制是一個精心設計的階梯式獎勵系統，確保：

1. **公平分配**：根據中心等級決定分潤比率
2. **避免重複**：上層中心只獲得差額分潤
3. **組織對應**：分潤關係遵循實際組織架構
4. **激勵提升**：鼓勵中心提升等級以獲得更多分潤

此機制在保證分潤公平性的同時，有效激勵了中心等級的提升和組織的健康發展。

---

**文檔版本**: 1.0  
**最後更新**: 2025-08-25  
**適用系統**: 積分回饋系統 v2.0
