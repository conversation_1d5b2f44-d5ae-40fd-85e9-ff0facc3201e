# 推廣獎勵分段分配機制說明

## 概述

本文檔說明系統中推廣獎勵的分段分配機制，特別是針對中脈課程的分配規則。此機制設計為階梯式獎勵系統，高級別合夥人可獲得多個分段的獎勵。

## 分配規則

### 基本原則

根據購買課程的級別，推廣獎勵會按照不同的burn_cv值進行分段分配：

1. **任督課程(vip_type_id=2)**: 任督burn_cv，由任督課程合伙人依權值分
2. **中脈課程(vip_type_id=3)**:
   - 任督burn_cv 由任督課程合伙人依權值分
   - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
3. **法身課程(vip_type_id=4)**:
   - 任督burn_cv 由任督課程合伙人依權值分
   - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
   - (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
4. **弟子課程(vip_type_id=5)**:
   - 任督burn_cv 由任督課程合伙人依權值分
   - (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
   - (法身burn_cv - 中脈burn_cv) 由法身以上課程的合伙人依權值分配
   - 余額 由弟子課程的合伙人依權值分配

### 課程等級對應

- **任督課程**: vip_type_course ≥ 2
- **中脈課程**: vip_type_course ≥ 3
- **法身課程**: vip_type_course ≥ 4
- **弟子課程**: vip_type_course ≥ 5

## 實際案例分析

### 案例：訂單907 - 中階中脈優惠套餐

**訂單基本信息：**
- 訂單編號：B20250630Q0UHIAHU
- 商品：中階中脈優惠套餐
- 會員級別獎勵：3（中脈課程）
- 總推廣獎勵CV：523.6

**分段計算結果：**
1. **任督CV部分**：184.8 CV（需求等級≥2）
2. **中脈CV差額部分**：338.8 CV（需求等級≥3）
3. **總計**：184.8 + 338.8 = 523.6 CV ✓

### 合夥人分配示例

以737、738、741號合夥人為例（課程等級均為5 - 弟子級別）：

#### 第一分段：任督CV部分（184.8 CV）
- **符合條件**：課程等級5 ≥ 2 ✓
- **737號獲得**：17.14 CV
- **738號獲得**：17.14 CV  
- **741號獲得**：17.14 CV

#### 第二分段：中脈CV差額部分（338.8 CV）
- **符合條件**：課程等級5 ≥ 3 ✓
- **737號獲得**：52.82 CV
- **738號獲得**：52.82 CV
- **741號獲得**：52.82 CV

#### 總計獲得
- **737號總計**：17.14 + 52.82 = 69.96 CV
- **738號總計**：17.14 + 52.82 = 69.96 CV
- **741號總計**：17.14 + 52.82 = 69.96 CV

## 常見疑問解答

### Q1: 為什麼同一個合夥人會獲得多次推廣獎勵？

**A1:** 這不是重複領取，而是分段分配機制的正常運作。高級別的合夥人同時符合多個分段的條件，因此會獲得多個分段的獎勵。

### Q2: 這樣的分配是否公平？

**A2:** 這是階梯式獎勵系統的設計理念：
- **低級別合夥人**：只能分到低級別分段的CV
- **高級別合夥人**：可以分到多個級別分段的CV
- **目的**：獎勵高級別合夥人，鼓勵合夥人提升課程等級

### Q3: 如何驗證分配的正確性？

**A3:** 可以通過以下方式驗證：
1. **分段總和**：各分段CV總和應等於總推廣獎勵CV
2. **條件檢查**：每個合夥人只能獲得符合其課程等級條件的分段獎勵
3. **權重比例**：同一分段內，獎勵按權重比例分配

## 技術實現

### 分段計算邏輯

```php
// 中脈課程分段計算示例
case 3: // 中脈課程
    // 任督burn_cv 由任督課程合伙人依權值分
    $rendu_cv = min($rendu_burn_cv, $product_cv);
    $rendu_ratio = $product_cv > 0 ? $rendu_cv / $product_cv : 0;
    $segments[] = [
        'cv' => $totalCV * $rendu_ratio,
        'required_level' => 2, // 任督級別
        'description' => '任督CV部分'
    ];

    // (中脈burn_cv - 任督burn_cv) 由中脈以上課程的合伙人依權值分配
    if ($product_cv > $rendu_burn_cv) {
        $zhongmai_diff_cv = min($zhongmai_burn_cv - $rendu_burn_cv, $product_cv - $rendu_burn_cv);
        $zhongmai_ratio = $product_cv > 0 ? $zhongmai_diff_cv / $product_cv : 0;
        $segments[] = [
            'cv' => $totalCV * $zhongmai_ratio,
            'required_level' => 3, // 中脈級別
            'description' => '中脈CV差額部分'
        ];
    }
    break;
```

### 合夥人篩選邏輯

```php
// 篩選符合該段要求的合伙人
foreach ($active_partners as $partner_id => $weight) {
    $partner_course_level = $user_cal_data[$partner_id]['data']['vip_type_course'] ?? 0;
    if ($partner_course_level >= $required_level) {
        $qualified_partners[$partner_id] = $weight;
        $qualified_total_weight += $weight;
    }
}
```

## 結論

推廣獎勵分段分配機制是一個精心設計的階梯式獎勵系統，旨在：

1. **公平分配**：根據合夥人的課程等級決定可獲得的分段獎勵
2. **激勵提升**：鼓勵合夥人提升課程等級以獲得更多獎勵
3. **精確計算**：確保每個分段的CV分配都按照權重比例進行

此機制確保了高級別合夥人獲得相應的獎勵，同時維持了整體分配的公平性和透明度。

---

**文檔版本**: 1.0  
**最後更新**: 2025-08-25  
**適用系統**: 積分回饋系統 v2.0
