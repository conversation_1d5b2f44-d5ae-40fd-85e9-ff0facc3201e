<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 優化 centers 表的索引
        Schema::connection('main_db')->table('centers', function (Blueprint $table) {
            // 複合索引用於列表查詢（名稱搜尋 + 狀態篩選）
            $table->index(['name', 'status'], 'idx_centers_name_status');

            // 複合索引用於等級篩選 + 狀態
            $table->index(['center_level_id', 'status'], 'idx_centers_level_status');

            // 創建時間索引用於排序
            $table->index('created_at', 'idx_centers_created_at');
        });

        // 優化 center_staff 表的索引
        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            // 複合索引用於查詢現役人員
            $table->index(['center_id', 'end_at'], 'idx_center_staff_center_active');

            // 複合索引用於角色查詢
            $table->index(['center_id', 'role_id', 'end_at'], 'idx_center_staff_role_active');

            // 複合索引用於會員角色查詢
            $table->index(['account_id', 'role_id', 'end_at'], 'idx_center_staff_account_role');
        });

        // 優化 account 表的搜尋索引
        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            // 會員編號搜尋索引（如果不存在）
            if (!$this->indexExists('account', 'idx_account_number_search')) {
                $table->index('number', 'idx_account_number_search');
            }

            // 複合索引用於狀態篩選
            if (!$this->indexExists('account', 'idx_account_number_status')) {
                $table->index(['number', 'status'], 'idx_account_number_status');
            }
        });

        // 優化 center_level 表
        // center_level 表無 status 欄位，不建立索引

        // 優化 center_roles 表
        Schema::connection('main_db')->table('center_roles', function (Blueprint $table) {
            // 角色代碼索引
            if (!$this->indexExists('center_roles', 'idx_center_roles_code')) {
                $table->index('code', 'idx_center_roles_code');
            }

            // 單例角色索引
            if (!$this->indexExists('center_roles', 'idx_center_roles_singleton')) {
                $table->index('is_singleton', 'idx_center_roles_singleton');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('main_db')->table('centers', function (Blueprint $table) {
            $table->dropIndex('idx_centers_name_status');
            $table->dropIndex('idx_centers_level_status');
            $table->dropIndex('idx_centers_created_at');
        });

        Schema::connection('main_db')->table('center_staff', function (Blueprint $table) {
            $table->dropIndex('idx_center_staff_center_active');
            $table->dropIndex('idx_center_staff_role_active');
            $table->dropIndex('idx_center_staff_account_role');
        });

        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            if ($this->indexExists('account', 'idx_account_number_search')) {
                $table->dropIndex('idx_account_number_search');
            }
            if ($this->indexExists('account', 'idx_account_number_status')) {
                $table->dropIndex('idx_account_number_status');
            }
        });

        // center_level 表無 status 欄位，不需移除索引

        Schema::connection('main_db')->table('center_roles', function (Blueprint $table) {
            if ($this->indexExists('center_roles', 'idx_center_roles_code')) {
                $table->dropIndex('idx_center_roles_code');
            }
            if ($this->indexExists('center_roles', 'idx_center_roles_singleton')) {
                $table->dropIndex('idx_center_roles_singleton');
            }
        });
    }

    /**
     * 檢查索引是否存在
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $indexes = DB::connection('main_db')
            ->select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);

        return !empty($indexes);
    }
};
