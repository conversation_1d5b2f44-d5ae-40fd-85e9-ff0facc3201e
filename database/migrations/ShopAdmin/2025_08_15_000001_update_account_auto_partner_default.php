<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            $table->integer('auto_partner')->default(2)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            $table->integer('auto_partner')->default(1)->change();
        });
    }
};
