<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;
use Database\Seeders\ShopAdmin\AccountSeeder;
use Database\Seeders\ShopAdmin\BasicAuthSeeder;
use Database\Seeders\ShopAdmin\BonusSettingSeeder;
use Database\Seeders\ShopAdmin\CenterLevelSeeder;
use Database\Seeders\ShopAdmin\ExcelSeeder;
use Database\Seeders\ShopAdmin\LangSeeder;
use Database\Seeders\ShopAdmin\LogisticsCodeSeeder;
use Database\Seeders\ShopAdmin\MessageStatusCodeSeeder;
use Database\Seeders\ShopAdmin\PartnerLevelSeeder;
use Database\Seeders\ShopAdmin\VipTypeSeeder;
use Database\Seeders\ShopAdmin\BonusModelSeeder;
use Database\Seeders\Shop\AboutStorySeeder;
use Database\Seeders\Shop\AdminSeeder;
use Database\Seeders\Shop\AdminInfoSeeder;
use Database\Seeders\Shop\BackstageMenuSeeder;
use Database\Seeders\Shop\BackstageMenuSecondSeeder;
use Database\Seeders\Shop\CitySeeder;
use Database\Seeders\Shop\ConsentSeeder;
use Database\Seeders\Shop\ContactSeeder;
use Database\Seeders\Shop\DefaultContentSeeder;
use Database\Seeders\Shop\DiscountSeeder;
use Database\Seeders\Shop\ExpiringProductSeeder;
use Database\Seeders\Shop\FrontendDataNameSeeder;
use Database\Seeders\Shop\FrontendMenuNameSeeder;
use Database\Seeders\Shop\HotProductSeeder;
use Database\Seeders\Shop\IndexExcelSeeder;
use Database\Seeders\Shop\IndexOnlineSeeder;
use Database\Seeders\Shop\PointsSettingSeeder;
use Database\Seeders\Shop\PositionSeeder;
use Database\Seeders\Shop\ProdescSeeder;
use Database\Seeders\Shop\ProductSeeder;
use Database\Seeders\Shop\ProductinfoSeeder;
use Database\Seeders\Shop\RecommendProductSeeder;
use Database\Seeders\Shop\SeoSeeder;
use Database\Seeders\Shop\ShippingFeeSeeder;
use Database\Seeders\Shop\SlideshowSeeder;
use Database\Seeders\Shop\SystemEmailSeeder;
use Database\Seeders\Shop\SystemIntroSeeder;
use Database\Seeders\Shop\TimeProductSeeder;
use Database\Seeders\Shop\TownSeeder;
use Database\Seeders\Shop\FieldsSetSeeder;
use Database\Seeders\Shop\ConsumptionDrawLimitSeeder;
use Database\Seeders\Shop\WanpayDataSeeder;
use Database\Seeders\Shop\ProductPriceSearchSeeder;
use Database\Seeders\Shop\PayFeeSeeder;
use Database\Seeders\Shop\PointsAllowUseSeeder;
use Database\Seeders\Shop\LovecodeSeeder;


class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            //------------ main_db (shop_admin)------------
            AccountSeeder::class,
            BasicAuthSeeder::class,
            BonusSettingSeeder::class,
            CenterLevelSeeder::class,
            ExcelSeeder::class,
            LangSeeder::class,
            LogisticsCodeSeeder::class,
            MessageStatusCodeSeeder::class,
            PartnerLevelSeeder::class,
            VipTypeSeeder::class,

            //----------------------(shop)-----------------------
            AboutStorySeeder::class,
            AdminSeeder::class,
            AdminInfoSeeder::class,
            BackstageMenuSeeder::class,
            BackstageMenuSecondSeeder::class,
            CitySeeder::class,
            ConsentSeeder::class,
            ContactSeeder::class,
            DefaultContentSeeder::class,
            DiscountSeeder::class,
            ExpiringProductSeeder::class,
            FrontendDataNameSeeder::class,
            FrontendMenuNameSeeder::class,
            HotProductSeeder::class,
            IndexExcelSeeder::class,
            IndexOnlineSeeder::class,
            PointsSettingSeeder::class,
            PositionSeeder::class,
            ProdescSeeder::class,
            ProductSeeder::class,
            ProductinfoSeeder::class,
            RecommendProductSeeder::class,
            SeoSeeder::class,
            ShippingFeeSeeder::class,
            SlideshowSeeder::class,
            SystemEmailSeeder::class,
            SystemIntroSeeder::class,
            TimeProductSeeder::class,
            TownSeeder::class,
            FieldsSetSeeder::class,
            ConsumptionDrawLimitSeeder::class,
            WanpayDataSeeder::class,
            ProductPriceSearchSeeder::class,
            PayFeeSeeder::class,
            PointsAllowUseSeeder::class,
            LovecodeSeeder::class,
            BonusModelSeeder::class,
        ]);
    }
}
