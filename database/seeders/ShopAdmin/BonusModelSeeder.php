<?php

namespace Database\Seeders\ShopAdmin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BonusModelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=bonus_model --output=ShopAdmin
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => '一般模組',
                'normal_recommend' => 20,
                'normal_partner' => 20,
                'normal_marketing_dept' => 5,
                'normal_sales_dept' => 7,
                'normal_executive_director' => 1,
                'normal_center_director' => 2,
                'normal_center_founder' => 2,
                'normal_lecturer' => 3,
                'normal_center' => 20,
                'normal_dividend_month' => 20,
                'normal_center_divided_to_raiser' => 0,
                'use_partner_mode' => 1,
                'partner_recommend' => 85,
                'partner_partner' => 0,
                'partner_marketing_dept' => 12,
                'partner_sales_dept' => 0,
                'partner_executive_director' => 0,
                'partner_center_director' => 0,
                'partner_center_founder' => 0,
                'partner_lecturer' => 3,
                'partner_center' => 0,
                'partner_dividend_month' => 0,
                'partner_center_divided_to_raiser' => 0,
                'ad_bonus' => 20,
            ],
            [
                'id' => 2,
                'name' => '不合夥批發',
                'normal_recommend' => 20,
                'normal_partner' => 20,
                'normal_marketing_dept' => 5,
                'normal_sales_dept' => 7,
                'normal_executive_director' => 1,
                'normal_center_director' => 2,
                'normal_center_founder' => 2,
                'normal_lecturer' => 3,
                'normal_center' => 20,
                'normal_dividend_month' => 20,
                'normal_center_divided_to_raiser' => 0,
                'use_partner_mode' => 0,
                'partner_recommend' => 0,
                'partner_partner' => 0,
                'partner_marketing_dept' => 0,
                'partner_sales_dept' => 0,
                'partner_executive_director' => 0,
                'partner_center_director' => 0,
                'partner_center_founder' => 0,
                'partner_lecturer' => 0,
                'partner_center' => 0,
                'partner_dividend_month' => 0,
                'partner_center_divided_to_raiser' => 0,
                'ad_bonus' => 20,
            ]
        ];
        DB::connection('main_db')->table('bonus_model')->truncate();
        DB::connection('main_db')->table("bonus_model")->insert($dataTables);
    }
}
