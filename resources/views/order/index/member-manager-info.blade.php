@extends('admin.Public.aside')
@section("title"){{$data['rowData']['number']}} - B會員功能管理@endsection
@section("css")
    <style>
        table, tr, td{
            position: relative;
            border: 1px solid #9D9D9D;
        }
        .inquiry td input{
            width:89%;
        }
        .inquiry td{
            border:none;
        }
        .inquiry td:nth-child(1) input{
            width:100%;
        }
    </style>
@endsection
@section("content")
    <div class="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">B會員功能管理</a></li>
            <li><a href="###">{{$data['rowData']['number']}}</a></li>
        </ul>
        <a class="back btn sendbtn" href="{{url('order/index/index')}}">
            <span  class="bi bi-arrow-left"></span>
        </a>
        <!--<button style="position:absolute; right:10px">刪除</button>-->
        <form action="{{url('order/index/updateMember')}}" name="editForm" method="post"  enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="id" value="{{$data['rowData']['id']}}">
            <div class="edit_form mt-3" style="min-width: 1366px;">
                <table class="table table-rwd ">
                    <tr>
                        <td>推薦者</td>
                        <td colspan="5">
                            @if($data['rowData']['up_user'])
                                <a href="{{url('order/Index/edit') . '?' . http_build_query(['id' => $data['rowData']['upline_user']])}}">
                                    {{$data['rowData']['up_user']['name']}}
                                </a>
                            @else
                                無
                            @endif
                            (
                                會員ID:
                                <input name="upline_user" type="number" value="{{$data['rowData']['up_user']['id']??0}}">
                            )
                        </td>
                        <td>來源類型</td>
                        <td colspan="3">
                            <select name="registration_from">
                                <option value="1" {{App\Services\CommonService::compare_return($data['rowData']['registration_from']??'', '1', 'selected')}}>一般</option>
                                <option value="2" {{App\Services\CommonService::compare_return($data['rowData']['registration_from']??'', '2', 'selected')}}>廣告</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>會員級別</td>
                        <td>
                            @if($data['rowData']['vip_id']==0)無@else{{$data['rowData']['vip_name']}}@endif
                        </td>
                        <td>合夥等級</td>
                        <td colspan="3">
                            {{$data['rowData']['partner_level_name']}}
                            (累積投資:{{$data['rowData']['partner_accumulation']}})
                        </td>
                        <td>中心等級</td>
                        <td colspan="3">
                            {{$data['rowData']['center_level_name']}}
                            (
                                發起者會員ID:
                                <input name="center_raiser_id" type="number" value="{{$data['rowData']['center_raiser_id']}}">
                                @if($data['rowData']['center_raiser_id'])
                                    <a href="{{url('order/Index/edit') . '?' . http_build_query(['id' => $data['rowData']['center_raiser_id']])}}">
                                        查看
                                    </a>
                                @endif
                            )
                        </td>
                    </tr>
                    <tr>
                        <td>會員編號</td>
                        <td>{{$data['rowData']['number']}}</td>
                        <td>近期修改日期</td>
                        <td colspan="3">{{$data['rowData']['update_time']}}</td>
                        <td>申請日期</td>
                        <td>{{ date('Y-m-d', intval($data['rowData']['createtime'])) }}</td>
                        <td colspan="2">
                            <input type="radio" name="status" value="0" id="待核"
                            @if($data['rowData']['status'] == "0") checked @endif
                            > <label for="待核">待核</label>
                            <input type="radio" name="status" value="1" id="通過"
                            @if($data['rowData']['status'] == "1") checked @endif
                            > <label for="通過">通過</label>
                            <input type="radio" name="status" value="2" id="黑名單"
                            @if($data['rowData']['status'] == "2") checked @endif
                            > <label for="黑名單">黑名單</label>
                            <input type="radio" name="status" value="3" id="停用"
                            @if($data['rowData']['status'] == "3") checked @endif
                            > <label for="停用">停用</label>
                        </td>
                    </tr>
                    <tr>
                        <td>會員姓名</td>
                        <td><input type="text" value="{{$data['rowData']['name']}}" name="name" class="w-100"></td>
                        <td>會員手機(帳號)</td>
                        <td colspan="3">
                            <input type="text" value="{{$data['rowData']['phone']}}" name="phone" class="w-100">
                        </td>
                        <td>會員密碼</td>
                        <td><input type="password" id="newPwd" placeholder="新密碼"><span class="change ml-1 btn sendbtn btn-sm" id="changePwd">修改</span></td>
                        <td>生日</td>
                        <td><input type="date" value="{{$data['rowData']['birthday']}}" name="birthday" class="w-100"></td>
                    </tr>
                    <tr>
                        <td>聯絡地址</td>
                        <td colspan="5"><input type="text" value="{{$data['rowData']['home']}}" name="F_S_NH_Address" class="w-100"></td>
                        <td>會員信箱</td>
                        <td>
                            <input type="text" value="{{$data['rowData']['email']}}" name="email" placeholder="請填入email" class="">
                            <a href="mailto:{{$data['rowData']['email']}}">寄信</a>
                        </td>
                        <td>聯絡電話</td>
                        <td><input type="text" value="{{$data['rowData']['tele']}}" name="tele" class="w-100"></td>
                    </tr>
                    
                    @if(empty(config('control.close_function_current')['會員瀏覽商品設定']))
                        <tr>
                            <td>瀏覽商品等級</td>
                            <td colspan="9">
                                <select name="product_view_id">
                                    @foreach($data['product_view'] as $vo)
                                        <option value="{{$vo['id']}}" {{App\Services\CommonService::compare_return($vo['id'], 'selected', $data['rowData']['product_view_id'])}}>{{$vo['name']}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                    @endif
                    @if(config('control.control_platform')==1)
                        <tr>
                            <td>會員類型</td>
                            <td colspan="5" class="text-left">
                                <input type="radio" name="user_type" value="0" id="一般會員"
                                @if($data['rowData']['user_type'] == "0") checked @endif
                                > <label for="一般會員" class="mr-3">一般會員</label>
                                <input type="radio" name="user_type" value="1" id="供應商"
                                @if($data['rowData']['user_type'] == "1") checked @endif
                                > <label for="供應商" class="mr-3">供應商</label>
                                <span class="text-danger ml-3">設定為「供應商」的會員ID才可填入商品詳細內容頁中的「供應商ID」</span>
                            </td>
                            <td>供應商回饋方式</td>
                            <td colspan="1" class="text-left">
                                <input type="radio" name="supplier_bonus" value="1" id="supplier_bonus_1"
                                @if($data['rowData']['supplier_bonus'] == "1") checked @endif
                                > <label for="supplier_bonus_1" class="mr-3">增值積分</label>
                                <input type="radio" name="supplier_bonus" value="2" id="supplier_bonus_2"
                                @if($data['rowData']['supplier_bonus'] == "2") checked @endif
                                > <label for="supplier_bonus_2" class="mr-3">現金</label>
                            </td>
                            <td>自動升級</td>
                            <td class="text-left">
                                <input type="radio" name="auto_partner" value="1" id="auto_partner_1"
                                @if($data['rowData']['auto_partner'] == "1") checked @endif
                                > <label for="auto_partner_1" class="mr-3">否</label>
                                <input type="radio" name="auto_partner" value="2" id="auto_partner_2"
                                @if($data['rowData']['auto_partner'] == "2") checked @endif
                                > <label for="auto_partner_2" class="mr-3">是</label>
                            </td>
                            <!-- <td>供應商資料</td>
                            <td colspan="7">
                                <input type="radio" name="user_type_radio" value="0" id="不開放"
                                @if($data['rowData']['user_type_radio'] == "0") checked @endif
                                > <label for="不開放">不開放</label>
                                <input type="radio" name="user_type_radio" value="1" id="開放"
                                @if($data['rowData']['user_type_radio'] == "1") checked @endif
                                > <label for="開放">開放</label>
                            </td> -->
                        </tr>
                        <tr>
                            <td>店鋪名稱</td>
                            <td colspan=""><input type="text" value="{{$data['rowData']['shop_name']}}" name="shop_name"></td>
                            <td>相關證件</td>
                            <td colspan="7">
                                <span class="mr-3">
                                    公司登記文件：
                                    @if($data['rowData']['file_company'])
                                        <a href="{{__UPLOAD__}}{{$data['rowData']['file_company']}}" target="_blank">(查看)</a>
                                    @else
                                        無
                                    @endif
                                </span>
                                <span class="mr-3">
                                    個人身份文件：
                                    @if($data['rowData']['file_person'])
                                        <a href="{{__UPLOAD__}}{{$data['rowData']['file_person']}}" target="_blank">(查看)</a>
                                    @else
                                        無
                                    @endif
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>收款帳號</td>
                            <td colspan="9">
                                <input type="text" value="{{$data['rowData']['bank']}}" name="bank" placeholder="銀行名稱">
                                <input type="text" value="{{$data['rowData']['bank_code']}}" name="bank_code" placeholder="分行代號">
                                <input type="text" value="{{$data['rowData']['bank_account_name']}}" name="bank_account_name" placeholder="戶名">
                                <input type="text" value="{{$data['rowData']['bank_account_code']}}" name="bank_account_code" placeholder="銀行帳號">
                            </td>
                        </tr>
                    @endif
                    <tr>
                        <td colspan="10" class="text-center"><button class="btn sendbtn">修改會員資料</button></td>
                    </tr>
                    <tr>
                        <!-- 
                            <td>完成訂單</td>
                            <td>{$data['rowData']['complete']}}</td>
                            <td>取消訂單</td>
                            <td>{$data['rowData']['cancel']}}</td>
                            <td>退貨</td>
                            <td>{$data['rowData']['return']}}</td>
                            <td>總消費金額</td>
                            <td>{$data['rowData']['totalcom']}}</td>
                        -->
                        <td>功德圓滿點數</td>
                        <td>{{$data['rowData']['increasing_limit_invest']}}</td>
                        <td>消費圓滿點數</td>
                        <td colspan="3">{{$data['rowData']['increasing_limit_consumption']}}</td>
                        <td>其他圓滿點數</td>
                        <td>{{$data['rowData']['increasing_limit_other']}}</td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td>增值積分</td>
                        <td>{{$data['rowData']['point_increasable']}}</td>
                        <td>現金積分</td>
                        <td colspan="3">{{$data['rowData']['point']}}</td>
                        <td colspan="4"></td>
                    </tr>
                    <tr>
                        <td colspan="10" class="pt-4">
                            <h3 class="main-title">推廣會員</h3>
                            <div class="listTable">
                                <table class="inquiry table-rwd table" style="width:calc(100% + 4px); margin:-2">
                                    <thead>
                                        <tr class="bg-dark">
                                            <th>會員ID</th>
                                            <th>會員編號</th>
                                            <th>姓名</th>
                                            <th style="width: 80px;">來源類型</th>
                                            @if(config('control.control_VipDiscount')==1)
                                                <th>會員級別</th>
                                            @endif
                                            <th>合夥等級</th>
                                            <th>中心等級</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($data['rowData']['down_users']) == true)
                                        <tr><td colspan='7' style='text-align: center;'><h2>沒有數據</h2></td></tr>
                                        @else
                                        @foreach($data['rowData']['down_users'] as $vo)
                                        <tr>
                                            <td><a href="{{url('order/Index/edit') . '?' . http_build_query(['id' => $vo['id']])}}">{{$vo['id']}}</a></td>
                                            <td>{{$vo['number']}}</td>
                                            <td>{{$vo['name']}}</td>
                                            <td>
                                                @if($vo['registration_from']==1)
                                                @elseif($vo['registration_from']==2)
                                                    廣告
                                                @endif
                                            </td>
                                            @if(config('control.control_VipDiscount')==1)
                                                <td>@if($vo['vip_id']==0)無@else{{$vo['vip_name']}}@endif</td>
                                            @endif
                                            <td>{{$vo['partner_level_name']}}</td>
                                            <td>{{$vo['center_level_name']}}</td>
                                        </tr>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="10" class="pt-4">
                            <h3 class="main-title">客戶購買紀錄</h3>
                            <div class="listTable">
                                <table class="inquiry table-rwd table" style="width:calc(100% + 4px); margin:-2">
                                    <thead>
                                        <tr class="bg-dark">
                                            <th>分店</th>
                                            <th>下單日期</th>
                                            <th>訂單狀態</th>
                                            <th>訂單編號</th>
                                            <th>總金額</th>
                                            <!-- <th>獲得紅利點數</th> -->
                                            <th style="width: 80px;">取消訂單</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($data['rowData']['order']) == true)
                                        <tr><td colspan='6' style='text-align: center;'><h2>沒有數據</h2></td></tr>
                                        @else
                                        @foreach($data['rowData']['order'] as $vo)
                                        <tr>
                                            <td>{{$vo['subDepartment']}}</td>
                                            <td>{{date('Y-m-d', $vo['create_time'])}}</td>
                                            <td>{{$vo['status']}}</td>
                                            <td><a href="{{url('/order/OrderCtrl/show') . '?' . http_build_query(['id' => $vo['id']])}}">{{$vo['order_number']}}</a></td>
                                            <td>{{$vo['total']}}</td>
                                            <!-- <td>{{$vo['add_point']}}</td> -->
                                            <td>
                                                <span class="bi bi-trash" onclick="operateCancel({{$vo['id']}})"></span>
                                            </td>
                                        </tr>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                    @if(empty(config('control.close_function_current')['註冊商品回函']))
                    <tr>
                        <td colspan="10" class="pt-4">
                            <h3 class="main-title">客戶商品註冊紀錄</h3>
                            <!--
                            <div style="float:right; margin-right:10px"><input type="date"> <input type="date"></div>
                            -->
                            <div class="listTable">
                                <table class="inquiry" style="width:calc(100% + 4px); margin:-2">
                                    <thead>
                                        <tr class="bg-dark">
                                            <th>註冊日期</th>
                                            <th>商品名稱</th>
                                            <th>機身碼</th>
                                            <th>購買日期</th>
                                            <th>發票號碼</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($data['reg_product']) == true)
                                        <tr><td colspan='5' style='text-align: center;'><h2>沒有數據</h2></td></tr>
                                        @else
                                        @foreach($data['reg_product'] as $vo)
                                        <tr>
                                            <td>{{$vo['regtime']}}</td>
                                            <td>{{$vo['product_name']}}</td>
                                            <td>{{$vo['product_code']}}</td>
                                            <td>{{$vo['buytime']}}</td>
                                            <td>{{$vo['tax_ID_number']}}</td>
                                        </tr>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                    @endif
                </table>
            </div>
           
        </form>
    </div>
@endsection
@section("ownJS")
    <script>
        /*更改密碼*/
        $('#changePwd').click(function (e){
            e.preventDefault();
            $.confirm({
                type: 'red',
                backgroundDismiss: true,
                title: '更改密碼',
                content: '此功能會強制更改用戶之密碼',
                buttons: {
                    sure: {
                        text: '確定更改', // With spaces and symbols
                        action: function () {
                            $newPwd = $('#newPwd').val();
                            if($newPwd == ''){
                                $.alert('新密碼不得為空');
                                return;
                            }
                            $.ajax({
                                url: "{{url('order/Index/changePassword')}}",
                                type: 'POST',
                                headers: {
                                    'X-CSRF-Token': csrf_token 
                                },
                                dataType: 'json',
                                data:{
                                    id: "{{$data['rowData']['id']}}",
                                    password: $newPwd
                                },
                                success: function(response) {
                                    $.alert(response.msg);
                                    $('#newPwd').val('');
                                },
                                error: function(xhr) {
                                    alert('更改失敗');
                                    console.log(xhr);
                                }
                            });
                        }
                    },
                    cancel: {
                        text: '取消'
                    }
                }
            });
        });
        /*更改帳號狀態*/
        $('input[name=status]:radio').change(function () {
            $.ajax({
                url: "{{url('order/Index/multiUpdate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data:{
                    id: "[{{$data['rowData']['id']}}]",
                    column: 'status',
                    value: $(this).val(),   
                },
                success: function(response) {
                    $.alert(response.msg);
                    $('#newPwd').val('');
                },
                error: function(xhr) {
                    $.alert('更改失敗');
                    console.log(xhr);
                }
            });
        });

        /*取消訂單*/
        function operateCancel($id) {
            if(confirm('確定取消嗎？')){
                $('#block_block').show();
                var form = document.createElement("form");
                form.action = "{{url('/order/OrderCtrl/changeStatus2Cancel')}}";
                form.method = "post";
                id = document.createElement("input");
                id.value = $id;
                id.name = "id";
                form.appendChild(id);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection