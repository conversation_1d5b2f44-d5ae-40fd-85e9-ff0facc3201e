@extends('admin.Public.aside')
@section('title')新增中心@endsection

@section('ownCSS')
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- CSRF Token for AJAX requests --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            <li><a href="###">新增中心</a></li>
        </ul>

        <div class="edit_form">
            <form method="POST" action="/order/center/store" id="centerForm">
                @csrf

                @if(isset($errors) && $errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif

                <!-- 中心基本資訊 -->
                <div class="form-section">
                    <h5 class="section-title">中心基本資訊</h5>
                    <div class="form-content">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <div class="form-field">
                                    <label for="name">中心名稱 <span class="required">*</span></label>
                                    <input type="text"
                                           class="form-control"
                                           id="name"
                                           name="name"
                                           placeholder="中心名稱"
                                           maxlength="64"
                                           required>
                                    <small class="form-text text-muted">最多64個字元</small>
                                </div>
                            </div>
                            <div class="form-group col-md-6">
                                <div class="form-field">
                                    <label for="center_level_id">中心等級 <span class="required">*</span></label>
                                    <select class="form-control" id="center_level_id" name="center_level_id" required>
                                        <option value="">請選擇中心等級</option>
                                        @if(isset($data['center_levels']))
                                            @foreach($data['center_levels'] as $level)
                                                <option value="{{ $level->id }}">{{ $level->name }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="status">狀態</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="1" selected>啟用</option>
                                    <option value="0">停用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色指派說明 -->
                <div class="form-section">
                    <h5 class="section-title">角色指派</h5>
                    <div class="form-content">
                        <p class="text-muted">可在建立中心後進行角色指派，或稍後在編輯頁面進行設定</p>
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="bi bi-plus-circle"></i> 建立中心
                    </button>
                    <a href="/order/center/index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 取消
                    </a>
                    <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('ownJS')
    <script>
        $(document).ready(function() {
            // 基本表單功能
            $('#centerForm').on('submit', function(e) {
                // 基本驗證
                var name = $('#name').val().trim();
                var centerLevelId = $('#center_level_id').val();

                if (!name) {
                    alert('請輸入中心名稱');
                    e.preventDefault();
                    return false;
                }

                if (!centerLevelId) {
                    alert('請選擇中心等級');
                    e.preventDefault();
                    return false;
                }

                // 顯示載入狀態
                $(this).find('button[type="submit"]').prop('disabled', true).text('建立中...');
            });

            // 重置按鈕功能
            $('#resetBtn').on('click', function() {
                if (confirm('確定要重置表單嗎？')) {
                    $('#centerForm')[0].reset();
                }
            });

            // 基本的鍵盤快捷鍵
            $(document).on('keydown', function(e) {
                // Ctrl+S 儲存
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    $('#centerForm').submit();
                }

                // Esc 取消
                if (e.keyCode === 27) {
                    if (confirm('確定要離開嗎？')) {
                        window.location.href = '/order/center/index';
                    }
                }
            });
        });
    </script>
@endsection
