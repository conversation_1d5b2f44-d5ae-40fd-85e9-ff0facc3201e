@extends('admin.Public.aside')
@section('title')新增中心@endsection

@section('ownCSS')
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- CSRF Token for AJAX requests --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            <li><a href="###">新增中心</a></li>
        </ul>

        <div class="edit_form">
            <form method="POST" action="/order/center/store" id="centerForm"
                  data-loading-form="true"
                  data-track-changes="true"
                  data-show-progress="true">
                @csrf
                {{-- Hidden field for center ID (will be populated after creation) --}}
                <input type="hidden" name="center_id" id="center_id" value="">

                @if(isset($errors) && $errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif

                <!-- 中心基本資訊 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">中心基本資訊</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">中心名稱 <span class="text-danger">*</span></label>
                                    <input type="text"
                                           class="form-control"
                                           id="name"
                                           name="name"
                                           value="{{ old('name') }}"
                                           placeholder="請輸入中心名稱"
                                           maxlength="64"
                                           data-validate="centerName"
                                           required>
                                    <small class="form-text text-muted">最多64個字元</small>
                                    <div class="invalid-feedback" id="name-error"></div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="center_level_id">中心等級 <span class="text-danger">*</span></label>
                                    <select class="form-control"
                                            id="center_level_id"
                                            name="center_level_id"
                                            data-validate="centerLevel"
                                            required>
                                        <option value="">請選擇中心等級</option>
                                        @foreach($data['center_levels'] as $level)
                                            <option value="{{ $level->id }}"
                                                    {{ old('center_level_id') == $level->id ? 'selected' : '' }}>
                                                {{ $level->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="center_level_id-error"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">狀態</label>
                                    <select class="form-control"
                                            id="status"
                                            name="status"
                                            data-validate="status">
                                        <option value="1" {{ old('status', '1') == '1' ? 'selected' : '' }}>啟用</option>
                                        <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色指派區域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">角色指派</h5>
                        <small class="text-muted">可在建立中心後進行角色指派，或稍後在編輯頁面進行設定</small>
                    </div>
                    <div class="card-body">
                        @include('admin.center.components.role-assignment')
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="form-group">
                    <button type="submit" class="btn btn-primary" id="submitBtn"
                            data-loading-button="true"
                            data-loading-text="建立中心中...">
                        <i class="bi bi-check-lg"></i> 建立中心
                    </button>
                    <a href="/order/center/index" class="btn btn-secondary">
                        <i class="bi bi-x-lg"></i> 取消
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('ownJS')
    {{-- jQuery UI JavaScript for autocomplete --}}
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <script>
        $(document).ready(function() {
            // 基本表單功能
            $('#centerForm').on('submit', function(e) {
                // 基本驗證
                var name = $('#name').val().trim();
                var centerLevelId = $('#center_level_id').val();

                if (!name) {
                    alert('請輸入中心名稱');
                    e.preventDefault();
                    return false;
                }

                if (!centerLevelId) {
                    alert('請選擇中心等級');
                    e.preventDefault();
                    return false;
                }

                // 顯示載入狀態
                $(this).find('button[type="submit"]').prop('disabled', true).text('建立中...');
            });

            // 重置按鈕功能
            $('#resetBtn').on('click', function() {
                if (confirm('確定要重置表單嗎？')) {
                    $('#centerForm')[0].reset();
                }
            });

            // 基本的鍵盤快捷鍵
            $(document).on('keydown', function(e) {
                // Ctrl+S 儲存
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    $('#centerForm').submit();
                }

                // Esc 取消
                if (e.keyCode === 27) {
                    if (confirm('確定要離開嗎？')) {
                        window.location.href = '/order/center/index';
                    }
                }
            });
                submitForm();
            });

            // 顯示驗證摘要
            function showValidationSummary() {
                const $form = $('#centerForm');
                const $errors = $form.find('.is-invalid');

                if ($errors.length > 0) {
                    let errorMessages = [];
                    $errors.each(function() {
                        const $field = $(this);
                        const fieldName = $field.closest('.form-group').find('label').text().replace('*', '').trim();
                        const errorMessage = $field.siblings('.invalid-feedback').text();
                        if (errorMessage) {
                            errorMessages.push(`${fieldName}: ${errorMessage}`);
                        }
                    });

                    if (errorMessages.length > 0) {
                        showErrorToast('請修正表單錯誤', errorMessages.join('<br>'));
                    }
                }
            }

            // 滾動到第一個錯誤欄位
            function scrollToFirstError() {
                const $firstError = $('.is-invalid').first();
                if ($firstError.length > 0) {
                    $('html, body').animate({
                        scrollTop: $firstError.offset().top - 100
                    }, 500);
                    $firstError.focus();
                }
            }

            // 增強的即時驗證
            $('#name').on('input', function() {
                const $this = $(this);
                const name = $this.val().trim();

                // 即時長度檢查
                if (name.length > 64) {
                    window.centerValidation.showFieldError($this, '中心名稱不能超過64個字元');
                } else if (name.length > 0 && name.length < 2) {
                    window.centerValidation.showFieldError($this, '中心名稱至少需要2個字元');
                } else {
                    window.centerValidation.clearFieldError($this);
                }

                // 更新字元計數
                updateCharacterCount('name', name.length, 64);
            });

            // 字元計數顯示
            function updateCharacterCount(fieldId, current, max) {
                let $counter = $(`#${fieldId}-counter`);
                if ($counter.length === 0) {
                    $counter = $(`<small class="form-text text-muted" id="${fieldId}-counter"></small>`);
                    $(`#${fieldId}`).after($counter);
                }

                const remaining = max - current;
                const color = remaining < 10 ? 'text-warning' : (remaining < 5 ? 'text-danger' : 'text-muted');
                $counter.removeClass('text-muted text-warning text-danger').addClass(color);
                $counter.text(`${current}/${max} 字元`);
            }
        });

        // 顯示欄位錯誤
        function showFieldError(fieldName, message) {
            const field = $('#' + fieldName);
            const errorDiv = $('#' + fieldName + '-error');

            field.addClass('is-invalid');
            errorDiv.text(message);
        }

        // 清除欄位錯誤
        function clearFieldError(fieldName) {
            const field = $('#' + fieldName);
            const errorDiv = $('#' + fieldName + '-error');

            field.removeClass('is-invalid');
            errorDiv.text('');
        }

        // 提交表單
        function submitForm() {
            // 使用 UX 增強的載入狀態
            window.centerUX.setButtonLoading($('#submitBtn'), true, '建立中心中...');
            window.centerUX.showGlobalLoading('正在建立中心，請稍候...');

            $.ajax({
                url: '/order/center/store',
                method: 'POST',
                data: $('#centerForm').serialize(),
                success: function(response) {
                    window.centerUX.hideGlobalLoading();
                    window.centerUX.setButtonLoading($('#submitBtn'), false);

                    if (response.status && response.data && response.data.id) {
                        // 設定中心ID供角色指派使用
                        $('#center_id').val(response.data.id);

                        // 顯示成功訊息
                        window.centerUX.showSuccess('建立成功', '中心已成功建立！', {
                            duration: 3000,
                            sound: true
                        });

                        // 延遲後重導向到編輯頁面或列表頁面
                        setTimeout(function() {
                            window.location.href = '/order/center/edit?id=' + response.data.id;
                        }, 1500);
                    } else {
                        // 成功但沒有返回中心ID，直接重導向到列表
                        window.centerUX.showSuccess('建立成功', '中心已成功建立！');
                        setTimeout(function() {
                            window.location.href = '/order/center/index';
                        }, 1500);
                    }
                },
                error: function(xhr) {
                    window.centerUX.hideGlobalLoading();
                    window.centerUX.setButtonLoading($('#submitBtn'), false);

                    if (xhr.status === 422) {
                        // 驗證錯誤
                        const errors = xhr.responseJSON.errors || {};

                        // 顯示驗證錯誤
                        Object.keys(errors).forEach(function(field) {
                            if (errors[field] && errors[field].length > 0) {
                                showFieldError(field, errors[field][0]);
                            }
                        });

                        // 顯示驗證錯誤摘要
                        const errorCount = Object.keys(errors).length;
                        const firstError = xhr.responseJSON.first_error || '資料驗證失敗';
                        window.centerUX.showError(
                            `發現 ${errorCount} 個驗證錯誤`,
                            firstError,
                            { duration: 8000 }
                        );

                        // 滾動到第一個錯誤欄位
                        scrollToFirstError();
                    } else {
                        // 其他錯誤
                        const message = xhr.responseJSON?.message || '建立中心時發生錯誤，請稍後再試';
                        const errorType = xhr.responseJSON?.error_type || 'system';

                        if (errorType === 'business_logic') {
                            window.centerUX.showWarning('操作受限', message);
                        } else {
                            window.centerUX.showError('系統錯誤', message);
                        }
                    }
                }
            });
        }

        // 通知系統（使用 UX 增強功能）
        function showSuccessMessage(message) {
            window.centerUX.showSuccess('成功', message);
        }

        function showErrorToast(title, message) {
            window.centerUX.showError(title, message);
        }

        function showWarningToast(title, message) {
            window.centerUX.showWarning(title, message);
        }

        function showInfoToast(title, message) {
            window.centerUX.showInfo(title, message);
        }

        // 增強的載入狀態管理
        function setLoadingState(isLoading, buttonSelector = '#submitBtn', loadingText = '處理中...') {
            const $button = $(buttonSelector);

            if (isLoading) {
                $button.data('original-text', $button.html());
                $button.prop('disabled', true)
                       .html(`<i class="bi bi-hourglass-split"></i> ${loadingText}`);
            } else {
                const originalText = $button.data('original-text');
                if (originalText) {
                    $button.prop('disabled', false).html(originalText);
                }
            }
        }

        // 表單重置功能
        function resetForm() {
            if (confirm('確定要重置表單嗎？所有輸入的資料將會清除。')) {
                $('#centerForm')[0].reset();
                window.centerValidation.resetForm($('#centerForm'));
                $('#center_id').val('');

                // 顯示重置通知
                window.centerUX.showInfo('表單重置', '表單已重置為初始狀態');

                // 焦點回到第一個欄位
                setTimeout(() => {
                    $('#name').focus();
                }, 100);
            }
        }

        // 增強的鍵盤快捷鍵支援（由 UX 增強系統處理）
        // 額外的頁面特定快捷鍵
        $(document).keydown(function(e) {
            // Ctrl+R 重置表單
            if (e.ctrlKey && e.keyCode === 82) {
                e.preventDefault();
                resetForm();
            }

            // F1 顯示說明
            if (e.keyCode === 112) {
                e.preventDefault();
                showHelp();
            }
        });

        // 顯示說明功能
        function showHelp() {
            const helpContent = `
                <div class="help-content">
                    <h6>中心建立說明</h6>
                    <ul>
                        <li><strong>中心名稱：</strong>必填，2-64個字元</li>
                        <li><strong>中心等級：</strong>必選，決定中心的層級</li>
                        <li><strong>狀態：</strong>預設為啟用</li>
                        <li><strong>角色指派：</strong>可在建立後進行設定</li>
                    </ul>
                    <hr>
                    <h6>鍵盤快捷鍵</h6>
                    <ul>
                        <li><kbd>Ctrl + S</kbd> - 儲存表單</li>
                        <li><kbd>Ctrl + R</kbd> - 重置表單</li>
                        <li><kbd>Esc</kbd> - 取消操作</li>
                        <li><kbd>F1</kbd> - 顯示說明</li>
                        <li><kbd>Ctrl + /</kbd> - 顯示所有快捷鍵</li>
                    </ul>
                </div>
            `;

            window.centerUX.showInfo('使用說明', helpContent, {
                duration: 15000,
                closable: true
            });
        }
    </script>
@endsection
