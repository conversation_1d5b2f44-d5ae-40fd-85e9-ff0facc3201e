{{-- 多人角色指派組件 --}}
<div class="multiple-role-container" data-role="{{ $role->code }}">
    {{-- 現有成員列表 --}}
    <div class="current-members mb-3">
        <h6 class="mb-2">
            現有成員
            <span class="badge badge-secondary members-count" style="display: none;"></span>
        </h6>
        <div class="members-list">
            {{-- 動態內容將由 JavaScript 或現有資料填充 --}}
        </div>
        <div class="no-members text-muted" style="display: none;">
            <small>尚未指派任何 {{ $role->name }}</small>
        </div>
    </div>

    {{-- 新增成員區域 --}}
    <div class="add-member-section">
        <div class="form-group mb-2">
            <label class="form-label">新增 {{ $role->name }}</label>
            <input type="text"
                   class="form-control account-autocomplete"
                   data-role="{{ $role->code }}"
                   data-singleton="0"
                   placeholder="輸入會員編號搜尋..."
                   autocomplete="off">
            <small class="form-text text-muted">
                輸入至少2個字元開始搜尋會員編號
            </small>
        </div>

        <button type="button" class="btn btn-sm btn-success assign-multiple-role">
            <i class="bi bi-person-plus"></i> 新增 {{ $role->name }}
        </button>
    </div>

    {{-- 載入現有資料 --}}
    @if(isset($center) && $center)
        @php
            $currentStaff = collect($center->activeStaff ?? [])->filter(function($staff) use ($role) {
                return $staff->role && $staff->role->code === $role->code;
            });
        @endphp

        @if($currentStaff->count() > 0)
            <script>
                $(document).ready(function() {
                    const $container = $('.multiple-role-container[data-role="{{ $role->code }}"]');
                    const $membersList = $container.find('.members-list');
                    const roleCode = '{{ $role->code }}';

                    // 添加現有成員
                    @foreach($currentStaff as $staff)
                        const memberHtml{{ $loop->index }} = `
                            <div class="member-item d-flex justify-content-between align-items-center mb-2" data-account-id="{{ $staff->account_id }}">
                                <span>
                                    <strong>{{ $staff->account->number ?? '' }}</strong>
                                    {{ $staff->account->name ? ' - ' . $staff->account->name : '' }}
                                    <small class="text-muted">
                                        ({{ $staff->start_at ? \Carbon\Carbon::parse($staff->start_at)->format('Y/m/d') : '' }} 起)
                                    </small>
                                </span>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-multiple-role"
                                        data-role="{{ $role->code }}" data-account-id="{{ $staff->account_id }}">
                                    <i class="bi bi-x"></i> 移除
                                </button>
                            </div>
                        `;
                        $membersList.append(memberHtml{{ $loop->index }});
                    @endforeach

                    // 更新成員數量
                    updateMembersCount($container);

                    // 隱藏無成員提示
                    $container.find('.no-members').hide();
                });
            </script>
        @else
            <script>
                $(document).ready(function() {
                    const $container = $('.multiple-role-container[data-role="{{ $role->code }}"]');
                    $container.find('.no-members').show();
                });
            </script>
        @endif
    @endif
</div>

<style>
.multiple-role-container {
    min-height: 100px;
}

.member-item {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.member-item strong {
    color: #495057;
}

.members-list:empty + .no-members {
    display: block !important;
}

.members-count {
    font-size: 0.75em;
}

.add-member-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    margin-top: 15px;
}

.form-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}
</style>
