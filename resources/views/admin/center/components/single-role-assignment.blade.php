{{-- 單一角色指派組件 --}}
<div class="single-role-container" data-role="{{ $role->code }}">
    {{-- 現有角色顯示 --}}
    <div class="selected-account" style="display: none;">
        {{-- 動態內容將由 JavaScript 填充 --}}
    </div>

    {{-- 角色指派輸入區 --}}
    <div class="role-input-section">
        <div class="form-group mb-2">
            <input type="text"
                   class="form-control account-autocomplete"
                   data-role="{{ $role->code }}"
                   data-singleton="1"
                   placeholder="輸入會員編號搜尋..."
                   autocomplete="off">
            <small class="form-text text-muted">
                輸入至少2個字元開始搜尋會員編號
            </small>
        </div>

        <div class="add-role-btn">
            <button type="button" class="btn btn-sm btn-primary assign-single-role">
                <i class="bi bi-person-plus"></i> 指派 {{ $role->name }}
            </button>
        </div>
    </div>

    {{-- 當已有角色時顯示的編輯按鈕 --}}
    <button type="button" class="btn btn-sm btn-outline-secondary show-single-input" style="display: none;">
        <i class="bi bi-pencil"></i> 更換 {{ $role->name }}
    </button>

    {{-- 載入現有資料 --}}
    @if(isset($center) && $center)
        @php
            $currentStaff = collect($center->activeStaff ?? [])->filter(function($staff) use ($role) {
                return $staff->role && $staff->role->code === $role->code;
            })->first();
        @endphp

        @if($currentStaff)
            <script>
                $(document).ready(function() {
                    // 顯示現有的角色指派
                    const $container = $('.single-role-container[data-role="{{ $role->code }}"]');
                    const $selectedDiv = $container.find('.selected-account');

                    $selectedDiv.html(`
                        <div class="selected-member d-flex justify-content-between align-items-center">
                            <span>
                                <strong>{{ $currentStaff->account->number ?? '' }}</strong>
                                {{ $currentStaff->account->name ? ' - ' . $currentStaff->account->name : '' }}
                            </span>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-single-role"
                                    data-role="{{ $role->code }}" data-account-id="{{ $currentStaff->account_id ?? '' }}">
                                <i class="bi bi-x"></i> 移除
                            </button>
                        </div>
                    `).show();

                    // 隱藏輸入區域，顯示編輯按鈕
                    $container.find('.role-input-section').hide();
                    $container.find('.show-single-input').show();
                });
            </script>
        @endif
    @endif
</div>

<style>
.single-role-container {
    min-height: 60px;
}

.selected-member {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 10px;
}

.selected-member strong {
    color: #495057;
}

.ui-autocomplete {
    max-height: 200px;
    overflow-y: auto;
    z-index: 1050;
}

.ui-menu-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
}

.ui-menu-item:hover {
    background-color: #f8f9fa;
}

.ui-state-active {
    background-color: #007bff !important;
    color: white !important;
    border: none !important;
}
</style>
