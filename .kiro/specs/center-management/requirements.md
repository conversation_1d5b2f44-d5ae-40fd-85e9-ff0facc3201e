# 中心管理功能需求文件

## 介紹

本功能為後台管理系統新增中心管理模組，提供完整的中心建立、修改、刪除和人員配置功能。管理員可以透過此功能管理各個中心的基本資訊和人員角色分配，包括大總監、中心總監、中心發起人、行政/廣告人員、講師和業務等角色的指派。

## 需求

### 需求 1：中心基本資料管理

**使用者故事：** 作為後台管理員，我希望能夠建立、修改和刪除中心，以便管理系統中的所有中心資訊。

#### 驗收標準

1. WHEN 管理員點擊新增中心按鈕 THEN 系統 SHALL 顯示中心建立表單
2. WHEN 管理員填寫中心名稱和選擇中心等級後提交 THEN 系統 SHALL 建立新的中心記錄
3. WHEN 管理員修改現有中心資訊 THEN 系統 SHALL 更新中心記錄並保存變更
4. WHEN 管理員嘗試刪除中心 THEN 系統 SHALL 檢查該中心是否有任何現役人員
5. IF 中心有現役人員 THEN 系統 SHALL 拒絕刪除並顯示錯誤訊息
6. IF 中心沒有現役人員 THEN 系統 SHALL 允許刪除中心

### 需求 2：中心等級選擇

**使用者故事：** 作為後台管理員，我希望能夠為中心設定等級，以便區分不同層級的中心。

#### 驗收標準

1. WHEN 管理員建立或修改中心時 THEN 系統 SHALL 提供下拉選單顯示所有可用的中心等級
2. WHEN 管理員選擇中心等級 THEN 系統 SHALL 將選擇的等級與中心關聯
3. WHEN 系統載入中心等級選項 THEN 系統 SHALL 從 center_level 表格取得資料

### 需求 3：單一角色人員指派（大總監、中心總監、中心發起人）

**使用者故事：** 作為後台管理員，我希望能夠為中心指派大總監、中心總監和中心發起人，以便建立中心的管理架構。

#### 驗收標準

1. WHEN 管理員設定大總監時 THEN 系統 SHALL 提供自動完成搜尋功能顯示會員編號
2. WHEN 管理員輸入會員編號關鍵字 THEN 系統 SHALL 從 account 表的 number 欄位搜尋並顯示匹配結果
3. WHEN 管理員選擇會員作為大總監 THEN 系統 SHALL 檢查該中心是否已有現役大總監
4. IF 中心已有現役大總監 THEN 系統 SHALL 結束舊任期並指派新的大總監
5. WHEN 管理員設定中心總監或中心發起人時 THEN 系統 SHALL 執行相同的單一角色指派邏輯
6. WHEN 系統指派單一角色時 THEN 系統 SHALL 確保每個中心每個角色只能有一位現役人員

### 需求 4：多人角色人員指派（行政/廣告人員、講師、業務）

**使用者故事：** 作為後台管理員，我希望能夠為中心指派多位行政/廣告人員、講師和業務，以便支援中心的多元化營運需求。

#### 驗收標準

1. WHEN 管理員設定行政/廣告人員時 THEN 系統 SHALL 允許選擇多位會員
2. WHEN 管理員新增行政/廣告人員 THEN 系統 SHALL 不限制該角色的現役人數
3. WHEN 管理員設定講師或業務時 THEN 系統 SHALL 執行相同的多人角色指派邏輯
4. WHEN 系統指派多人角色時 THEN 系統 SHALL 允許同一中心同一角色有多位現役人員
5. WHEN 管理員移除多人角色成員 THEN 系統 SHALL 結束該人員的任期但不影響其他同角色人員

### 需求 5：人員搜尋和自動完成

**使用者故事：** 作為後台管理員，我希望能夠快速搜尋和選擇會員，以便有效率地進行人員指派。

#### 驗收標準

1. WHEN 管理員在人員選擇欄位輸入文字 THEN 系統 SHALL 即時搜尋匹配的會員編號
2. WHEN 系統執行搜尋 THEN 系統 SHALL 從 account 表的 number 欄位進行模糊搜尋
3. WHEN 搜尋結果顯示 THEN 系統 SHALL 顯示會員編號和相關識別資訊
4. WHEN 管理員選擇搜尋結果 THEN 系統 SHALL 自動填入選擇的會員資訊
5. WHEN 搜尋無結果 THEN 系統 SHALL 顯示「無匹配結果」訊息

### 需求 6：中心列表和管理介面

**使用者故事：** 作為後台管理員，我希望能夠查看所有中心的列表和詳細資訊，以便進行整體管理。

#### 驗收標準

1. WHEN 管理員進入中心管理頁面 THEN 系統 SHALL 顯示所有中心的列表
2. WHEN 系統顯示中心列表 THEN 系統 SHALL 包含中心名稱、等級、狀態和操作按鈕
3. WHEN 管理員點擊編輯按鈕 THEN 系統 SHALL 開啟中心編輯表單並預填現有資料
4. WHEN 管理員點擊刪除按鈕 THEN 系統 SHALL 顯示確認對話框
5. WHEN 管理員確認刪除 THEN 系統 SHALL 執行刪除前檢查並處理結果
6. WHEN 系統顯示中心詳情 THEN 系統 SHALL 顯示所有現役人員的角色分配

### 需求 7：資料驗證和錯誤處理

**使用者故事：** 作為後台管理員，我希望系統能夠驗證輸入資料並提供清楚的錯誤訊息，以便確保資料的正確性。

#### 驗收標準

1. WHEN 管理員提交空的中心名稱 THEN 系統 SHALL 顯示「中心名稱為必填」錯誤訊息
2. WHEN 管理員未選擇中心等級 THEN 系統 SHALL 顯示「請選擇中心等級」錯誤訊息
3. WHEN 管理員選擇不存在的會員編號 THEN 系統 SHALL 顯示「會員編號不存在」錯誤訊息
4. WHEN 系統發生資料庫錯誤 THEN 系統 SHALL 顯示友善的錯誤訊息並記錄詳細錯誤
5. WHEN 管理員嘗試刪除有現役人員的中心 THEN 系統 SHALL 顯示「無法刪除：中心仍有現役人員」訊息

### 需求 8：權限控制和安全性

**使用者故事：** 作為系統管理員，我希望確保只有授權的管理員能夠存取中心管理功能，以便維護系統安全。

#### 驗收標準

1. WHEN 未授權使用者嘗試存取中心管理功能 THEN 系統 SHALL 拒絕存取並重導向到登入頁面
2. WHEN 授權管理員存取功能 THEN 系統 SHALL 允許完整的中心管理操作
3. WHEN 系統執行敏感操作（如刪除）THEN 系統 SHALL 記錄操作日誌
4. WHEN 管理員進行人員指派 THEN 系統 SHALL 驗證操作者的權限等級
