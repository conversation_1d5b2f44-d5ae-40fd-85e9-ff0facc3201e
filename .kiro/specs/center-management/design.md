# 中心管理功能設計文件

## 概述

中心管理功能是一個完整的後台管理模組，提供中心的 CRUD 操作和人員角色管理。該功能將整合到現有的後台管理系統中，遵循現有的架構模式和設計規範。

基於現有的資料模型（Center、CenterLevel、CenterRole、CenterStaff、Account）和服務層（CenterStaffService），我們將建立一個用戶友好的管理介面，支援中心的完整生命週期管理。

## 架構

### 系統架構圖

```mermaid
graph TB
    A[前端介面] --> B[控制器層]
    B --> C[服務層]
    C --> D[模型層]
    D --> E[資料庫層]

    subgraph "控制器層"
        B1[CenterController]
    end

    subgraph "服務層"
        C1[CenterService]
        C2[CenterStaffService - 現有]
    end

    subgraph "模型層"
        D1[Center]
        D2[CenterLevel]
        D3[CenterRole]
        D4[CenterStaff]
        D5[Account]
    end

    subgraph "資料庫層"
        E1[centers]
        E2[center_level]
        E3[center_roles]
        E4[center_staff]
        E5[account]
    end
```

### 技術棧

-   **後端框架**: Laravel (基於現有系統)
-   **前端**: Blade 模板 + jQuery + Bootstrap 4 (遵循現有後台風格)
-   **資料庫**: MySQL (使用現有的 main_db 連線)
-   **UI 組件**: jQuery UI (自動完成功能)
-   **樣式**: Bootstrap 4 + 現有後台 CSS

## 組件和介面

### 1. 控制器層 (CenterController)

**位置**: `app/Http/Controllers/admin/Center.php`

**主要方法**:

-   `index()` - 中心列表頁面
-   `create()` - 顯示新增中心表單
-   `store()` - 處理新增中心請求
-   `show($id)` - 顯示中心詳情
-   `edit($id)` - 顯示編輯中心表單
-   `update($id)` - 處理更新中心請求
-   `destroy($id)` - 刪除中心
-   `getAccountSuggestions()` - AJAX 會員編號自動完成
-   `assignRole()` - 指派角色
-   `removeRole()` - 移除角色

**路由設計**:

```php
Route::group(['prefix' => 'admin'], function () {
    Route::get('/center/index', 'Center@index');
    Route::get('/center/create', 'Center@create');
    Route::post('/center/store', 'Center@store');
    Route::get('/center/show/{id}', 'Center@show');
    Route::get('/center/edit/{id}', 'Center@edit');
    Route::post('/center/update/{id}', 'Center@update');
    Route::post('/center/destroy/{id}', 'Center@destroy');
    Route::get('/center/account-suggestions', 'Center@getAccountSuggestions');
    Route::post('/center/assign-role', 'Center@assignRole');
    Route::post('/center/remove-role', 'Center@removeRole');
});
```

### 2. 服務層

#### CenterService (新建)

**位置**: `app/Services/Center/CenterService.php`

**主要功能**:

-   中心的 CRUD 操作
-   中心刪除前的安全檢查
-   會員搜尋和自動完成
-   資料驗證和業務邏輯處理

**主要方法**:

```php
class CenterService
{
    public function getAllCenters($filters = [])
    public function createCenter(array $data)
    public function updateCenter(int $id, array $data)
    public function deleteCenter(int $id)
    public function canDeleteCenter(int $id): bool
    public function searchAccounts(string $query): Collection
    public function getCenterWithStaff(int $id)
    public function validateCenterData(array $data): array
}
```

#### CenterStaffService (現有，擴展)

現有的 `CenterStaffService` 已經提供了完整的角色指派功能，我們將直接使用：

-   `assignRole()` - 指派角色
-   `endActiveRole()` - 結束角色
-   `getActiveStaff()` - 取得現役人員
-   `hasActiveSingleton()` - 檢查單例角色

### 3. 視圖層

#### 主要視圖文件

**位置**: `resources/views/admin/center/`

1. **index.blade.php** - 中心列表頁面

    - 顯示所有中心的表格
    - 搜尋和篩選功能
    - 新增、編輯、刪除按鈕

2. **create.blade.php** - 新增中心表單

    - 中心名稱輸入
    - 中心等級下拉選單
    - 角色指派區塊

3. **edit.blade.php** - 編輯中心表單

    - 預填現有資料
    - 角色管理介面
    - 現役人員顯示

4. **show.blade.php** - 中心詳情頁面
    - 中心基本資訊
    - 所有角色和人員列表
    - 歷史記錄

#### 視圖組件設計

**角色指派組件**:

```html
<div class="role-assignment">
    <h5>{{ $role->name }}</h5>
    <div class="form-group">
        <input
            type="text"
            class="form-control account-autocomplete"
            data-role="{{ $role->code }}"
            data-singleton="{{ $role->is_singleton }}"
            placeholder="輸入會員編號搜尋..."
        />
    </div>
    <div class="current-staff">
        <!-- 顯示現役人員 -->
    </div>
</div>
```

## 資料模型

### 資料流程圖

```mermaid
graph LR
    A[中心管理介面] --> B[表單驗證]
    B --> C[CenterService]
    C --> D[資料庫操作]

    E[角色指派] --> F[CenterStaffService]
    F --> G[交易處理]
    G --> H[角色記錄]

    I[會員搜尋] --> J[Account 查詢]
    J --> K[自動完成結果]
```

### 主要資料結構

**中心表單資料**:

```php
[
    'name' => 'string|required|max:64',
    'center_level_id' => 'integer|required|exists:center_level,id',
    'status' => 'integer|in:0,1',
    'roles' => [
        'founder' => 'integer|nullable|exists:account,id',
        'director' => 'integer|nullable|exists:account,id',
        'executive_director' => 'integer|nullable|exists:account,id',
        'lecturer' => 'array|nullable',
        'market' => 'array|nullable',
        'sales' => 'array|nullable',
    ]
]
```

**角色指派資料**:

```php
[
    'center_id' => 'integer|required',
    'account_id' => 'integer|required',
    'role_code' => 'string|required|in:founder,director,executive_director,lecturer,market,sales',
    'start_at' => 'datetime|nullable',
    'note' => 'string|nullable|max:255'
]
```

## 錯誤處理

### 錯誤類型和處理策略

1. **驗證錯誤**

    - 前端即時驗證
    - 後端表單驗證
    - 友善錯誤訊息顯示

2. **業務邏輯錯誤**

    - 中心刪除檢查（有現役人員時拒絕）
    - 單例角色衝突處理
    - 會員不存在錯誤

3. **系統錯誤**
    - 資料庫連線錯誤
    - 交易失敗處理
    - 異常日誌記錄

### 錯誤處理流程

```mermaid
graph TD
    A[用戶操作] --> B{驗證通過?}
    B -->|否| C[顯示驗證錯誤]
    B -->|是| D{業務邏輯檢查}
    D -->|失敗| E[顯示業務錯誤]
    D -->|通過| F[執行操作]
    F --> G{操作成功?}
    G -->|否| H[顯示系統錯誤]
    G -->|是| I[顯示成功訊息]
```

## 測試策略

### 單元測試

1. **CenterService 測試**

    - 中心 CRUD 操作
    - 刪除前檢查邏輯
    - 會員搜尋功能

2. **CenterController 測試**

    - HTTP 請求處理
    - 表單驗證
    - 權限檢查

3. **模型關聯測試**
    - Center 與 CenterLevel 關聯
    - Center 與 CenterStaff 關聯
    - 資料完整性檢查

### 整合測試

1. **完整流程測試**

    - 中心建立到角色指派
    - 角色變更流程
    - 中心刪除流程

2. **併發測試**
    - 單例角色併發指派
    - 資料庫交易完整性

### 用戶介面測試

1. **功能測試**

    - 表單提交和驗證
    - AJAX 自動完成
    - 動態介面更新

2. **瀏覽器相容性**
    - 主流瀏覽器測試
    - 響應式設計檢查

## 安全考量

### 權限控制

1. **存取控制**

    - 管理員身份驗證
    - 功能權限檢查
    - 操作日誌記錄

2. **資料保護**
    - SQL 注入防護
    - XSS 攻擊防護
    - CSRF 保護

### 資料驗證

1. **輸入驗證**

    - 前端即時驗證
    - 後端嚴格驗證
    - 資料清理和過濾

2. **業務規則驗證**
    - 角色指派規則
    - 資料完整性檢查
    - 操作權限驗證

## 效能優化

### 資料庫優化

1. **查詢優化**

    - 適當的索引使用
    - 關聯查詢優化
    - 分頁處理

2. **快取策略**
    - 中心等級快取
    - 角色資料快取
    - 會員搜尋結果快取

### 前端優化

1. **載入優化**

    - 按需載入 JavaScript
    - CSS 壓縮和合併
    - 圖片優化

2. **用戶體驗**
    - 載入狀態顯示
    - 操作回饋
    - 錯誤提示優化

## 部署和維護

### 部署流程

1. **資料庫遷移**

    - 確認現有資料表結構
    - 執行必要的索引優化
    - 資料完整性檢查

2. **程式碼部署**

    - 控制器和服務類別
    - 視圖模板
    - 靜態資源

3. **設定更新**
    - 路由註冊
    - 權限設定
    - 快取清理

### 維護計劃

1. **監控指標**

    - 系統效能監控
    - 錯誤率追蹤
    - 用戶操作統計

2. **定期維護**
    - 資料庫優化
    - 日誌清理
    - 安全更新

## 未來擴展

### 功能擴展

1. **進階功能**

    - 批量操作
    - 資料匯入匯出
    - 報表生成

2. **整合功能**
    - 通知系統整合
    - 工作流程整合
    - API 介面提供

### 技術升級

1. **前端現代化**

    - Vue.js 或 React 整合
    - 更好的用戶體驗
    - 行動裝置優化

2. **後端優化**
    - 微服務架構
    - 快取系統升級
    - 效能監控強化
