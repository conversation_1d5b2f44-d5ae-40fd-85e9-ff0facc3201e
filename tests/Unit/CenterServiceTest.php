<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Center\CenterService;
use App\Services\Center\ValidationService;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery;

/**
 * CenterService 單元測試
 *
 * 測試需求：
 * - 1.1: 中心 CRUD 操作
 * - 1.3: 修改現有中心資訊
 * - 1.5: 檢查該中心是否有任何現役人員
 * - 5.1, 5.2: 會員搜尋和自動完成功能
 */
class CenterServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $centerService;
    protected $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->centerService = new CenterService();
        $this->validationService = new ValidationService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_be_instantiated()
    {
        $this->assertInstanceOf(CenterService::class, $this->centerService);
    }

    /** @test */
    public function it_has_required_methods()
    {
        $requiredMethods = [
            'getAllCenters',
            'createCenter',
            'updateCenter',
            'deleteCenter',
            'canDeleteCenter',
            'searchAccounts',
            'getCenterWithStaff',
            'validateCenterData',
            'accountExists',
            'getCenterStats'
        ];

        foreach ($requiredMethods as $method) {
            $this->assertTrue(
                method_exists($this->centerService, $method),
                "CenterService should have method: {$method}"
            );
        }
    }

    /** @test */
    public function it_validates_center_data_successfully()
    {
        $validData = [
            'name' => '測試中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        // Mock CenterLevel exists check
        $this->mockCenterLevelExists(1, true);

        $result = $this->centerService->validateCenterData($validData);

        $this->assertArrayHasKey('name', $result);
        $this->assertArrayHasKey('center_level_id', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertEquals('測試中心', $result['name']);
        $this->assertEquals(1, $result['center_level_id']);
        $this->assertEquals(1, $result['status']);
    }

    /** @test */
    public function it_rejects_empty_center_name()
    {
        $invalidData = [
            'name' => '',
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->expectException(ValidationException::class);
        $this->centerService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_rejects_center_name_too_long()
    {
        $invalidData = [
            'name' => str_repeat('很長的中心名稱', 10), // 超過64字元
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->expectException(ValidationException::class);
        $this->centerService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_rejects_invalid_center_level()
    {
        $invalidData = [
            'name' => '測試中心',
            'center_level_id' => 999, // 不存在的等級
            'status' => 1
        ];

        // Mock CenterLevel exists check
        $this->mockCenterLevelExists(999, false);

        $this->expectException(ValidationException::class);
        $this->centerService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_rejects_invalid_status_values()
    {
        $invalidData = [
            'name' => '測試中心',
            'center_level_id' => 1,
            'status' => 2 // 無效狀態
        ];

        $this->mockCenterLevelExists(1, true);

        $this->expectException(ValidationException::class);
        $this->centerService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_can_check_if_center_can_be_deleted()
    {
        $centerId = 1;

        // Mock no active staff
        $this->mockCenterStaffActive($centerId, false);

        $canDelete = $this->centerService->canDeleteCenter($centerId);
        $this->assertTrue($canDelete);

        // Mock has active staff
        $this->mockCenterStaffActive($centerId, true);

        $canDelete = $this->centerService->canDeleteCenter($centerId);
        $this->assertFalse($canDelete);
    }

    /** @test */
    public function it_can_search_accounts()
    {
        $query = 'test';
        $limit = 10;

        // Mock account search results
        $mockAccounts = collect([
            (object)[
                'id' => 1,
                'number' => 'test001',
                'name' => '測試用戶1',
                'email' => '<EMAIL>',
                'status' => 1
            ],
            (object)[
                'id' => 2,
                'number' => 'test002',
                'name' => '測試用戶2',
                'email' => '<EMAIL>',
                'status' => 1
            ]
        ]);

        $this->mockAccountSearch($query, $limit, $mockAccounts);

        $results = $this->centerService->searchAccounts($query, $limit);

        $this->assertCount(2, $results);
        $this->assertEquals('test001', $results->first()['number']);
        $this->assertArrayHasKey('display', $results->first());
    }

    /** @test */
    public function it_returns_empty_collection_for_empty_search_query()
    {
        $results = $this->centerService->searchAccounts('', 10);
        $this->assertTrue($results->isEmpty());
    }

    /** @test */
    public function it_limits_search_results()
    {
        $query = 'test';
        $limit = 2;

        // Mock more results than limit
        $mockAccounts = collect([
            (object)['id' => 1, 'number' => 'test001', 'name' => '測試1', 'email' => '', 'status' => 1],
            (object)['id' => 2, 'number' => 'test002', 'name' => '測試2', 'email' => '', 'status' => 1],
            (object)['id' => 3, 'number' => 'test003', 'name' => '測試3', 'email' => '', 'status' => 1],
        ]);

        $this->mockAccountSearch($query, $limit, $mockAccounts->take($limit));

        $results = $this->centerService->searchAccounts($query, $limit);

        $this->assertCount($limit, $results);
    }

    /** @test */
    public function it_can_check_if_account_exists()
    {
        $accountId = 1;

        // Mock account exists
        $this->mockAccountExists($accountId, true);
        $this->assertTrue($this->centerService->accountExists($accountId));

        // Mock account doesn't exist
        $this->mockAccountExists($accountId, false);
        $this->assertFalse($this->centerService->accountExists($accountId));
    }

    /** @test */
    public function it_handles_validation_errors_in_search()
    {
        // Test with invalid query (too short)
        $results = $this->centerService->searchAccounts('a', 10);
        $this->assertTrue($results->isEmpty());

        // Test with invalid limit (too high)
        $results = $this->centerService->searchAccounts('test', 100);
        $this->assertTrue($results->isEmpty());
    }

    /** @test */
    public function it_logs_search_operations()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('會員搜尋執行', Mockery::type('array'));

        $this->mockAccountSearch('test', 10, collect());

        $this->centerService->searchAccounts('test', 10);
    }

    /** @test */
    public function it_handles_search_exceptions_gracefully()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')
            ->once()
            ->with('會員搜尋執行失敗', Mockery::type('array'));

        // Mock database exception
        Account::shouldReceive('query')
            ->andThrow(new \Exception('Database error'));

        $results = $this->centerService->searchAccounts('test', 10);
        $this->assertTrue($results->isEmpty());
    }

    /** @test */
    public function it_validates_center_name_uniqueness()
    {
        $data = [
            'name' => '重複中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        // Mock center name exists
        $this->mockCenterNameExists('重複中心', true);
        $this->mockCenterLevelExists(1, true);

        $this->expectException(ValidationException::class);
        $this->centerService->validateCenterData($data);
    }

    /** @test */
    public function it_allows_same_name_when_updating_same_center()
    {
        $data = [
            'name' => '現有中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        $excludeId = 1;

        // Mock center name exists but for different ID
        $this->mockCenterNameExistsExcluding('現有中心', $excludeId, false);
        $this->mockCenterLevelExists(1, true);

        $result = $this->centerService->validateCenterData($data, $excludeId);
        $this->assertEquals('現有中心', $result['name']);
    }

    /** @test */
    public function it_trims_center_name()
    {
        $data = [
            'name' => '  測試中心  ',
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->mockCenterLevelExists(1, true);

        $result = $this->centerService->validateCenterData($data);
        $this->assertEquals('測試中心', $result['name']);
    }

    /** @test */
    public function it_converts_data_types_correctly()
    {
        $data = [
            'name' => '測試中心',
            'center_level_id' => '1', // String
            'status' => '0' // String
        ];

        $this->mockCenterLevelExists(1, true);

        $result = $this->centerService->validateCenterData($data);
        $this->assertIsInt($result['center_level_id']);
        $this->assertIsInt($result['status']);
        $this->assertEquals(1, $result['center_level_id']);
        $this->assertEquals(0, $result['status']);
    }

    /** @test */
    public function it_can_create_center_successfully()
    {
        $data = [
            'name' => '新測試中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        // Mock validation
        $this->mockCenterLevelExists(1, true);
        $this->mockCenterNameExists('新測試中心', false);

        // Mock Center creation
        $mockCenter = Mockery::mock(Center::class);
        $mockCenter->id = 1;
        $mockCenter->name = '新測試中心';
        $mockCenter->center_level_id = 1;
        $mockCenter->status = 1;
        $mockCenter->created_at = now();

        $mockCenter->shouldReceive('load')
            ->with(['level', 'activeStaff.role', 'activeStaff.account'])
            ->andReturnSelf();

        Center::shouldReceive('create')
            ->with([
                'name' => '新測試中心',
                'center_level_id' => 1,
                'status' => 1
            ])
            ->andReturn($mockCenter);

        // Mock transaction
        DB::shouldReceive('connection')
            ->with('main_db')
            ->andReturnSelf();
        DB::shouldReceive('transaction')
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        $result = $this->centerService->createCenter($data);

        $this->assertInstanceOf(Center::class, $result);
        $this->assertEquals('新測試中心', $result->name);
    }

    /** @test */
    public function it_can_update_center_successfully()
    {
        $centerId = 1;
        $data = [
            'name' => '更新後的中心',
            'center_level_id' => 2,
            'status' => 0
        ];

        // Mock existing center
        $mockCenter = Mockery::mock(Center::class);
        $mockCenter->id = $centerId;
        $mockCenter->name = '原始中心';
        $mockCenter->center_level_id = 1;
        $mockCenter->status = 1;

        $mockCenter->shouldReceive('update')
            ->with([
                'name' => '更新後的中心',
                'center_level_id' => 2,
                'status' => 0
            ])
            ->once();

        $mockCenter->shouldReceive('load')
            ->with(['level', 'activeStaff.role', 'activeStaff.account'])
            ->andReturnSelf();

        Center::shouldReceive('findOrFail')
            ->with($centerId)
            ->andReturn($mockCenter);

        // Mock validation
        $this->mockCenterLevelExists(2, true);
        $this->mockCenterNameExistsExcluding('更新後的中心', $centerId, false);

        // Mock transaction
        DB::shouldReceive('connection')
            ->with('main_db')
            ->andReturnSelf();
        DB::shouldReceive('transaction')
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        $result = $this->centerService->updateCenter($centerId, $data);

        $this->assertInstanceOf(Center::class, $result);
    }

    /** @test */
    public function it_can_delete_center_when_no_active_staff()
    {
        $centerId = 1;

        // Mock center
        $mockCenter = Mockery::mock(Center::class);
        $mockCenter->id = $centerId;
        $mockCenter->name = '測試中心';
        $mockCenter->center_level_id = 1;
        $mockCenter->status = 1;
        $mockCenter->created_at = now();

        $mockCenter->shouldReceive('delete')
            ->once()
            ->andReturn(true);

        Center::shouldReceive('findOrFail')
            ->with($centerId)
            ->andReturn($mockCenter);

        // Mock no active staff
        $this->mockCenterStaffActive($centerId, false);

        // Mock transaction
        DB::shouldReceive('connection')
            ->with('main_db')
            ->andReturnSelf();
        DB::shouldReceive('transaction')
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Mock logging
        \Log::shouldReceive('info')->twice();

        $result = $this->centerService->deleteCenter($centerId);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_throws_exception_when_deleting_center_with_active_staff()
    {
        $centerId = 1;

        // Mock center
        $mockCenter = Mockery::mock(Center::class);
        $mockCenter->id = $centerId;
        $mockCenter->name = '有人員的中心';

        Center::shouldReceive('findOrFail')
            ->with($centerId)
            ->andReturn($mockCenter);

        // Mock has active staff
        $this->mockCenterStaffActive($centerId, true);

        // Mock staff count for error message
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(3);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('無法刪除：中心「有人員的中心」仍有 3 位現役人員');

        $this->centerService->deleteCenter($centerId);
    }

    /** @test */
    public function it_gets_center_with_staff_successfully()
    {
        $centerId = 1;

        // Mock center with relationships
        $mockCenter = Mockery::mock(Center::class);
        $mockCenter->id = $centerId;

        Center::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('with')
            ->with([
                'level',
                'staff.role',
                'staff.account',
                'activeStaff.role',
                'activeStaff.account'
            ])
            ->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->with($centerId)
            ->andReturn($mockCenter);

        $result = $this->centerService->getCenterWithStaff($centerId);

        $this->assertInstanceOf(Center::class, $result);
        $this->assertEquals($centerId, $result->id);
    }

    /** @test */
    public function it_gets_center_stats_successfully()
    {
        $centerId = 1;
        $expectedStats = [
            'total_staff' => 5,
            'active_staff' => 3,
            'roles_filled' => 4
        ];

        // Mock cache service
        $this->centerService = Mockery::mock(CenterService::class)->makePartial();
        $mockCacheService = Mockery::mock();
        $mockCacheService->shouldReceive('getCenterStats')
            ->with($centerId)
            ->andReturn($expectedStats);

        // Use reflection to set the cache service
        $reflection = new \ReflectionClass($this->centerService);
        $property = $reflection->getProperty('cacheService');
        $property->setAccessible(true);
        $property->setValue($this->centerService, $mockCacheService);

        $result = $this->centerService->getCenterStats($centerId);

        $this->assertEquals($expectedStats, $result);
        $this->assertArrayHasKey('total_staff', $result);
        $this->assertArrayHasKey('active_staff', $result);
        $this->assertArrayHasKey('roles_filled', $result);
    }

    /** @test */
    public function it_gets_all_center_levels_successfully()
    {
        $expectedLevels = collect([
            (object)['id' => 1, 'name' => '一級中心'],
            (object)['id' => 2, 'name' => '二級中心']
        ]);

        // Mock cache service
        $this->centerService = Mockery::mock(CenterService::class)->makePartial();
        $mockCacheService = Mockery::mock();
        $mockCacheService->shouldReceive('getCenterLevels')
            ->andReturn($expectedLevels);

        // Use reflection to set the cache service
        $reflection = new \ReflectionClass($this->centerService);
        $property = $reflection->getProperty('cacheService');
        $property->setAccessible(true);
        $property->setValue($this->centerService, $mockCacheService);

        $result = $this->centerService->getAllCenterLevels();

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /** @test */
    public function it_handles_null_status_correctly()
    {
        $data = [
            'name' => '測試中心',
            'center_level_id' => 1
            // status not provided
        ];

        $this->mockCenterLevelExists(1, true);

        $result = $this->centerService->validateCenterData($data);
        $this->assertNull($result['status']);
    }

    // Helper methods for mocking

    protected function mockCenterLevelExists(int $levelId, bool $exists)
    {
        CenterLevel::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('id', $levelId)
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($exists);
    }

    protected function mockCenterStaffActive(int $centerId, bool $hasActive)
    {
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($hasActive);
    }

    protected function mockAccountSearch(string $query, int $limit, $results)
    {
        Account::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('number', 'like', '%' . $query . '%')
            ->andReturnSelf()
            ->shouldReceive('select')
            ->with(['id', 'number', 'name', 'email', 'status'])
            ->andReturnSelf()
            ->shouldReceive('limit')
            ->with(Mockery::type('int'))
            ->andReturnSelf()
            ->shouldReceive('orderBy')
            ->with('number')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn($results);
    }

    protected function mockAccountExists(int $accountId, bool $exists)
    {
        Account::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('id', $accountId)
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($exists);
    }

    protected function mockCenterNameExists(string $name, bool $exists)
    {
        Center::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('name', $name)
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($exists);
    }

    protected function mockCenterNameExistsExcluding(string $name, int $excludeId, bool $exists)
    {
        Center::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('name', $name)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('id', '!=', $excludeId)
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($exists);
    }
}
