<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Center\CenterStaffService;
use App\Models\Main\Center;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Mockery;

/**
 * 角色指派邏輯測試
 *
 * 測試需求：
 * - 3.4: 單例角色衝突處理邏輯
 * - 3.5: 結束舊任期並指派新的角色
 * - 3.6: 確保每個中心每個角色只能有一位現役人員
 * - 4.3: 多人角色的新增邏輯
 * - 4.4: 不限制該角色的現役人數
 * - 4.5: 移除多人角色成員不影響其他同角色人員
 */
class CenterRoleAssignmentTest extends TestCase
{
    use RefreshDatabase;

    protected $centerStaffService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->centerStaffService = new CenterStaffService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_be_instantiated()
    {
        $this->assertInstanceOf(CenterStaffService::class, $this->centerStaffService);
    }

    /** @test */
    public function it_has_required_methods()
    {
        $requiredMethods = [
            'assignRole',
            'endRole',
            'endActiveRole',
            'renewRole',
            'getActiveStaff',
            'hasActiveSingleton'
        ];

        foreach ($requiredMethods as $method) {
            $this->assertTrue(
                method_exists($this->centerStaffService, $method),
                "CenterStaffService should have method: {$method}"
            );
        }
    }

    /** @test */
    public function it_can_assign_role_to_new_staff()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $startAt = Carbon::now();

        // Mock role exists and is assignable
        $this->mockRole($roleCode, false, true); // Not singleton, assignable

        // Mock center exists
        $this->mockCenterExists($centerId);

        // Mock no existing active staff
        $this->mockNoActiveStaff($centerId, $roleCode);

        // Mock successful creation
        $mockStaff = $this->mockCenterStaffCreation($centerId, $accountId, 1, $startAt);

        $result = $this->centerStaffService->assignRole($centerId, $accountId, $roleCode, $startAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_throws_exception_for_non_assignable_role()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'invalid_role';

        // Mock role doesn't exist or not assignable
        $this->mockRole($roleCode, false, false);

        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage("CenterRole not assignable: {$roleCode}");

        $this->centerStaffService->assignRole($centerId, $accountId, $roleCode);
    }

    /** @test */
    public function it_throws_exception_for_non_existent_center()
    {
        $centerId = 999;
        $accountId = 1;
        $roleCode = 'director';

        // Mock role exists
        $this->mockRole($roleCode, false, true);

        // Mock center doesn't exist
        $this->mockCenterExists($centerId, false);

        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage("Center not found: {$centerId}");

        $this->centerStaffService->assignRole($centerId, $accountId, $roleCode);
    }

    /** @test */
    public function it_handles_singleton_role_conflict_with_auto_end()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $startAt = Carbon::now();

        // Mock singleton role
        $this->mockRole($roleCode, true, true); // Singleton, assignable

        // Mock center exists
        $this->mockCenterExists($centerId);

        // Mock existing active singleton staff
        $existingStaff = $this->mockExistingActiveStaff($centerId, $roleCode);

        // Mock successful creation of new staff
        $mockStaff = $this->mockCenterStaffCreation($centerId, $accountId, 1, $startAt);

        $result = $this->centerStaffService->assignRole($centerId, $accountId, $roleCode, $startAt, true);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_throws_exception_for_singleton_conflict_without_auto_end()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';

        // Mock singleton role
        $this->mockRole($roleCode, true, true);

        // Mock center exists
        $this->mockCenterExists($centerId);

        // Mock existing active singleton staff
        $this->mockExistingActiveStaff($centerId, $roleCode);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage("Singleton role 'director' already active in center 1");

        $this->centerStaffService->assignRole($centerId, $accountId, $roleCode, null, false);
    }

    /** @test */
    public function it_can_end_active_role()
    {
        $centerStaffId = 1;
        $endAt = Carbon::now();

        // Mock existing staff
        $mockStaff = Mockery::mock(CenterStaff::class);
        $mockStaff->shouldReceive('getAttribute')
            ->with('end_at')
            ->andReturn(null);
        $mockStaff->shouldReceive('setAttribute')
            ->with('end_at', $endAt);
        $mockStaff->shouldReceive('save')
            ->once();

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->with($centerStaffId)
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->endRole($centerStaffId, $endAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_does_not_end_already_ended_role()
    {
        $centerStaffId = 1;
        $endAt = Carbon::now();
        $existingEndAt = Carbon::yesterday();

        // Mock staff that's already ended
        $mockStaff = Mockery::mock(CenterStaff::class);
        $mockStaff->shouldReceive('getAttribute')
            ->with('end_at')
            ->andReturn($existingEndAt);
        $mockStaff->shouldNotReceive('setAttribute');
        $mockStaff->shouldNotReceive('save');

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->with($centerStaffId)
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->endRole($centerStaffId, $endAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_can_end_active_role_by_criteria()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $endAt = Carbon::now();

        // Mock role exists
        $this->mockRole($roleCode, false, true, 1);

        // Mock existing active staff
        $mockStaff = Mockery::mock(CenterStaff::class);
        $mockStaff->shouldReceive('setAttribute')
            ->with('end_at', $endAt);
        $mockStaff->shouldReceive('save')
            ->once();

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('account_id', $accountId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->endActiveRole($centerId, $accountId, $roleCode, $endAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_returns_null_when_no_active_role_to_end()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';

        // Mock role exists
        $this->mockRole($roleCode, false, true, 1);

        // Mock no active staff
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('account_id', $accountId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->centerStaffService->endActiveRole($centerId, $accountId, $roleCode);

        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_null_when_role_not_found_for_ending()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'invalid_role';

        // Mock role doesn't exist
        CenterRole::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('code', $roleCode)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->centerStaffService->endActiveRole($centerId, $accountId, $roleCode);

        $this->assertNull($result);
    }

    /** @test */
    public function it_can_get_active_staff_for_role()
    {
        $centerId = 1;
        $roleCode = 'lecturer';

        // Mock role exists
        $this->mockRole($roleCode, false, true, 1);

        // Mock active staff collection
        $mockStaff = collect([
            Mockery::mock(CenterStaff::class),
            Mockery::mock(CenterStaff::class)
        ]);

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->getActiveStaff($centerId, $roleCode);

        $this->assertCount(2, $result);
    }

    /** @test */
    public function it_returns_empty_collection_for_non_existent_role()
    {
        $centerId = 1;
        $roleCode = 'invalid_role';

        // Mock role doesn't exist
        CenterRole::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('code', $roleCode)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->centerStaffService->getActiveStaff($centerId, $roleCode);

        $this->assertTrue($result->isEmpty());
    }

    /** @test */
    public function it_can_check_if_singleton_role_is_active()
    {
        $centerId = 1;
        $roleCode = 'director';

        // Mock singleton role exists
        $this->mockRole($roleCode, true, true, 1);

        // Mock has active singleton
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn(true);

        $result = $this->centerStaffService->hasActiveSingleton($centerId, $roleCode);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_returns_false_for_non_singleton_role()
    {
        $centerId = 1;
        $roleCode = 'lecturer';

        // Mock non-singleton role
        $this->mockRole($roleCode, false, true, 1);

        $result = $this->centerStaffService->hasActiveSingleton($centerId, $roleCode);

        $this->assertFalse($result);
    }

    /** @test */
    public function it_returns_false_for_non_existent_role_in_singleton_check()
    {
        $centerId = 1;
        $roleCode = 'invalid_role';

        // Mock role doesn't exist
        CenterRole::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('code', $roleCode)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('is_singleton', 1)
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);

        $result = $this->centerStaffService->hasActiveSingleton($centerId, $roleCode);

        $this->assertFalse($result);
    }

    // Helper methods for mocking

    protected function mockRole(string $roleCode, bool $isSingleton, bool $isAssignable, int $id = 1)
    {
        $mockRole = null;
        if ($isAssignable) {
            $mockRole = Mockery::mock(CenterRole::class);
            $mockRole->shouldReceive('isSingleton')
                ->andReturn($isSingleton);
            $mockRole->id = $id;
        }

        CenterRole::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('code', $roleCode)
            ->andReturnSelf();

        if ($isSingleton) {
            CenterRole::shouldReceive('where')
                ->with('is_singleton', 1)
                ->andReturnSelf();
        }

        if ($isAssignable) {
            CenterRole::shouldReceive('where')
                ->with('is_assignable', 1)
                ->andReturnSelf();
        }

        CenterRole::shouldReceive('first')
            ->andReturn($mockRole);
    }

    protected function mockCenterExists(int $centerId, bool $exists = true)
    {
        Center::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('whereKey')
            ->with($centerId)
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn($exists);
    }

    protected function mockNoActiveStaff(int $centerId, string $roleCode)
    {
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('lockForUpdate')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);
    }

    protected function mockExistingActiveStaff(int $centerId, string $roleCode)
    {
        $mockStaff = Mockery::mock(CenterStaff::class);
        $mockStaff->shouldReceive('setAttribute')
            ->with('end_at', Mockery::type(Carbon::class));
        $mockStaff->shouldReceive('save')
            ->once();

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('lockForUpdate')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn($mockStaff);

        return $mockStaff;
    }

    protected function mockCenterStaffCreation(int $centerId, int $accountId, int $roleId, Carbon $startAt)
    {
        $mockStaff = Mockery::mock(CenterStaff::class);

        CenterStaff::shouldReceive('create')
            ->with([
                'center_id' => $centerId,
                'account_id' => $accountId,
                'role_id' => $roleId,
                'start_at' => $startAt,
                'note' => null,
            ])
            ->andReturn($mockStaff);

        return $mockStaff;
    }

    /** @test */
    public function it_can_renew_existing_role()
    {
        $centerStaffId = 1;
        $newStartAt = Carbon::now();

        // Mock existing staff
        $mockStaff = Mockery::mock(CenterStaff::class);
        $mockStaff->shouldReceive('setAttribute')
            ->with('start_at', $newStartAt);
        $mockStaff->shouldReceive('setAttribute')
            ->with('end_at', null);
        $mockStaff->shouldReceive('save')
            ->once();

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('findOrFail')
            ->with($centerStaffId)
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->renewRole($centerStaffId, $newStartAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_handles_multiple_role_assignments_for_non_singleton()
    {
        $centerId = 1;
        $roleCode = 'lecturer'; // Non-singleton role

        // Mock non-singleton role
        $this->mockRole($roleCode, false, true, 1);

        // Mock center exists
        $this->mockCenterExists($centerId);

        // Mock no existing staff (first assignment)
        $this->mockNoActiveStaff($centerId, $roleCode);

        // Mock successful creation
        $mockStaff1 = $this->mockCenterStaffCreation($centerId, 1, 1, Carbon::now());
        $result1 = $this->centerStaffService->assignRole($centerId, 1, $roleCode);

        // Second assignment to same role should succeed
        $mockStaff2 = $this->mockCenterStaffCreation($centerId, 2, 1, Carbon::now());
        $result2 = $this->centerStaffService->assignRole($centerId, 2, $roleCode);

        $this->assertInstanceOf(CenterStaff::class, $result1);
        $this->assertInstanceOf(CenterStaff::class, $result2);
    }

    /** @test */
    public function it_validates_role_assignment_business_rules()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';

        // Test that service validates business rules
        $this->assertTrue(method_exists($this->centerStaffService, 'validateRoleAssignment'));
        $this->assertTrue(method_exists($this->centerStaffService, 'canAssignRole'));
        $this->assertTrue(method_exists($this->centerStaffService, 'getRoleConflicts'));
    }

    /** @test */
    public function it_handles_role_assignment_with_custom_start_date()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $customStartAt = Carbon::now()->addDays(7); // Future start date

        // Mock singleton role
        $this->mockRole($roleCode, true, true, 1);

        // Mock center exists
        $this->mockCenterExists($centerId);

        // Mock no existing active staff
        $this->mockNoActiveStaff($centerId, $roleCode);

        // Mock successful creation with custom start date
        $mockStaff = $this->mockCenterStaffCreation($centerId, $accountId, 1, $customStartAt);

        $result = $this->centerStaffService->assignRole($centerId, $accountId, $roleCode, $customStartAt);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_handles_role_assignment_with_notes()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $note = '特殊指派說明';

        // Mock role and center
        $this->mockRole($roleCode, true, true, 1);
        $this->mockCenterExists($centerId);
        $this->mockNoActiveStaff($centerId, $roleCode);

        // Mock creation with note
        $mockStaff = Mockery::mock(CenterStaff::class);
        CenterStaff::shouldReceive('create')
            ->with(Mockery::on(function ($data) use ($note) {
                return $data['note'] === $note;
            }))
            ->andReturn($mockStaff);

        $result = $this->centerStaffService->assignRole($centerId, $accountId, $roleCode, null, false, $note);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_gets_role_assignment_history()
    {
        $centerId = 1;
        $roleCode = 'director';

        // Mock role exists
        $this->mockRole($roleCode, true, true, 1);

        // Mock historical staff records
        $mockHistory = collect([
            Mockery::mock(CenterStaff::class),
            Mockery::mock(CenterStaff::class)
        ]);

        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('orderBy')
            ->with('start_at', 'desc')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn($mockHistory);

        $result = $this->centerStaffService->getRoleHistory($centerId, $roleCode);

        $this->assertCount(2, $result);
    }

    /** @test */
    public function it_validates_account_eligibility_for_role()
    {
        $accountId = 1;
        $roleCode = 'director';

        // Test that service can validate account eligibility
        $this->assertTrue(method_exists($this->centerStaffService, 'isAccountEligibleForRole'));
        $this->assertTrue(method_exists($this->centerStaffService, 'getAccountRoleConflicts'));
    }

    /** @test */
    public function it_handles_bulk_role_assignments()
    {
        $centerId = 1;
        $roleCode = 'lecturer'; // Multi-person role
        $accountIds = [1, 2, 3];

        // Test that service supports bulk operations
        $this->assertTrue(method_exists($this->centerStaffService, 'assignMultipleRoles'));
        $this->assertTrue(method_exists($this->centerStaffService, 'bulkEndRoles'));
    }

    /** @test */
    public function it_handles_role_transfer_between_centers()
    {
        $fromCenterId = 1;
        $toCenterId = 2;
        $accountId = 1;
        $roleCode = 'director';

        // Test that service supports role transfers
        $this->assertTrue(method_exists($this->centerStaffService, 'transferRole'));
        $this->assertTrue(method_exists($this->centerStaffService, 'validateRoleTransfer'));
    }

    /** @test */
    public function it_provides_role_assignment_statistics()
    {
        $centerId = 1;

        // Test that service provides statistics
        $this->assertTrue(method_exists($this->centerStaffService, 'getCenterRoleStats'));
        $this->assertTrue(method_exists($this->centerStaffService, 'getAccountRoleStats'));
    }

    /** @test */
    public function it_handles_concurrent_singleton_role_assignments()
    {
        // This tests the locking mechanism for singleton roles
        $centerId = 1;
        $roleCode = 'director';

        // Mock singleton role
        $this->mockRole($roleCode, true, true, 1);
        $this->mockCenterExists($centerId);

        // Mock lockForUpdate in the query
        CenterStaff::shouldReceive('query')
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('center_id', $centerId)
            ->andReturnSelf()
            ->shouldReceive('where')
            ->with('role_id', 1)
            ->andReturnSelf()
            ->shouldReceive('active')
            ->andReturnSelf()
            ->shouldReceive('lockForUpdate')
            ->andReturnSelf()
            ->shouldReceive('first')
            ->andReturn(null);

        // Mock successful creation
        $mockStaff = $this->mockCenterStaffCreation($centerId, 1, 1, Carbon::now());

        $result = $this->centerStaffService->assignRole($centerId, 1, $roleCode);

        $this->assertInstanceOf(CenterStaff::class, $result);
    }

    /** @test */
    public function it_validates_role_assignment_date_constraints()
    {
        $centerId = 1;
        $accountId = 1;
        $roleCode = 'director';
        $pastDate = Carbon::now()->subDays(30);

        // Test that service validates date constraints
        $this->assertTrue(method_exists($this->centerStaffService, 'validateAssignmentDates'));
        $this->assertTrue(method_exists($this->centerStaffService, 'canAssignRoleAtDate'));
    }
}
