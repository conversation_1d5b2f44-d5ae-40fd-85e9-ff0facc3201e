<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Center\CenterService;

/**
 * 中心刪除功能測試
 *
 * 測試需求：
 * - 1.5: 檢查該中心是否有任何現役人員
 * - 1.6: 如果中心沒有現役人員則允許刪除中心
 * - 6.5: 刪除確認對話框
 * - 7.4: 刪除操作的日誌記錄
 */
class CenterDeletionTest extends TestCase
{
    /**
     * 測試 CenterService 類別存在且可以實例化
     */
    public function test_center_service_can_be_instantiated()
    {
        $centerService = new CenterService();
        $this->assertInstanceOf(CenterService::class, $centerService);
    }

    /**
     * 測試 canDeleteCenter 方法存在
     */
    public function test_can_delete_center_method_exists()
    {
        $centerService = new CenterService();
        $this->assertTrue(method_exists($centerService, 'canDeleteCenter'));
    }

    /**
     * 測試 deleteCenter 方法存在
     */
    public function test_delete_center_method_exists()
    {
        $centerService = new CenterService();
        $this->assertTrue(method_exists($centerService, 'deleteCenter'));
    }

    /**
     * 測試 validateCenterData 方法存在
     */
    public function test_validate_center_data_method_exists()
    {
        $centerService = new CenterService();
        $this->assertTrue(method_exists($centerService, 'validateCenterData'));
    }

    /**
     * 測試刪除功能的基本邏輯結構
     */
    public function test_deletion_logic_structure()
    {
        // 這個測試驗證刪除相關方法的存在性和基本結構
        $centerService = new CenterService();

        // 驗證關鍵方法存在
        $this->assertTrue(method_exists($centerService, 'canDeleteCenter'));
        $this->assertTrue(method_exists($centerService, 'deleteCenter'));
        $this->assertTrue(method_exists($centerService, 'getCenterWithStaff'));

        // 驗證這些方法是公開的
        $reflection = new \ReflectionClass($centerService);
        $this->assertTrue($reflection->getMethod('canDeleteCenter')->isPublic());
        $this->assertTrue($reflection->getMethod('deleteCenter')->isPublic());
        $this->assertTrue($reflection->getMethod('getCenterWithStaff')->isPublic());
    }
}
