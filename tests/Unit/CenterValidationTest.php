<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Center\ValidationService;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CenterValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validationService = new ValidationService();
    }

    /** @test */
    public function it_validates_center_name_successfully()
    {
        $validData = [
            'name' => '測試中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        // 這應該不會拋出異常
        $result = $this->validationService->validateCenterData($validData);

        $this->assertArrayHasKey('name', $result);
        $this->assertEquals('測試中心', $result['name']);
    }

    /** @test */
    public function it_rejects_empty_center_name()
    {
        $invalidData = [
            'name' => '',
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_rejects_center_name_too_long()
    {
        $invalidData = [
            'name' => str_repeat('很長的中心名稱', 10), // 超過64字元
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_rejects_center_name_with_forbidden_words()
    {
        $forbiddenWords = ['測試', 'test', 'demo', '假', '虛擬', '臨時'];

        foreach ($forbiddenWords as $word) {
            $invalidData = [
                'name' => "包含{$word}的中心",
                'center_level_id' => 1,
                'status' => 1
            ];

            try {
                $this->validationService->validateCenterData($invalidData);
                $this->fail("應該拒絕包含禁用詞彙「{$word}」的中心名稱");
            } catch (ValidationException $e) {
                $this->assertStringContainsString($word, $e->getMessage());
            }
        }
    }

    /** @test */
    public function it_rejects_invalid_center_name_pattern()
    {
        $invalidNames = [
            '<script>alert("xss")</script>',
            'center@#$%',
            'center<>',
            'center"quotes"'
        ];

        foreach ($invalidNames as $name) {
            $invalidData = [
                'name' => $name,
                'center_level_id' => 1,
                'status' => 1
            ];

            try {
                $this->validationService->validateCenterData($invalidData);
                $this->fail("應該拒絕無效格式的中心名稱：{$name}");
            } catch (ValidationException $e) {
                $this->assertTrue(true); // 驗證通過
            }
        }
    }

    /** @test */
    public function it_validates_account_search_successfully()
    {
        $validData = [
            'query' => 'user123',
            'limit' => 10
        ];

        $result = $this->validationService->validateAccountSearchData($validData);

        $this->assertArrayHasKey('query', $result);
        $this->assertEquals('user123', $result['query']);
    }

    /** @test */
    public function it_rejects_short_search_query()
    {
        $invalidData = [
            'query' => 'a', // 只有1個字元
            'limit' => 10
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateAccountSearchData($invalidData);
    }

    /** @test */
    public function it_rejects_dangerous_search_patterns()
    {
        $dangerousQueries = [
            'SELECT * FROM users',
            '<script>alert("xss")</script>',
            'user\' OR 1=1--',
            'javascript:alert(1)',
            'UNION SELECT password FROM users'
        ];

        foreach ($dangerousQueries as $query) {
            $invalidData = [
                'query' => $query,
                'limit' => 10
            ];

            try {
                $this->validationService->validateAccountSearchData($invalidData);
                $this->fail("應該拒絕危險的搜尋查詢：{$query}");
            } catch (ValidationException $e) {
                $this->assertTrue(true); // 驗證通過
            }
        }
    }

    /** @test */
    public function it_validates_role_assignment_successfully()
    {
        $validData = [
            'center_id' => 1,
            'account_id' => 1,
            'role_code' => 'director',
            'start_at' => now()->addDay()->format('Y-m-d H:i:s'),
            'note' => '測試備註'
        ];

        $result = $this->validationService->validateRoleAssignmentData($validData);

        $this->assertArrayHasKey('center_id', $result);
        $this->assertArrayHasKey('account_id', $result);
        $this->assertArrayHasKey('role_code', $result);
    }

    /** @test */
    public function it_rejects_invalid_role_code()
    {
        $invalidData = [
            'center_id' => 1,
            'account_id' => 1,
            'role_code' => 'invalid_role',
            'start_at' => now()->addDay()->format('Y-m-d H:i:s')
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateRoleAssignmentData($invalidData);
    }

    /** @test */
    public function it_rejects_past_start_date()
    {
        $invalidData = [
            'center_id' => 1,
            'account_id' => 1,
            'role_code' => 'director',
            'start_at' => now()->subDay()->format('Y-m-d H:i:s') // 過去的日期
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateRoleAssignmentData($invalidData);
    }

    /** @test */
    public function it_validates_center_deletion_with_no_active_staff()
    {
        // 假設中心ID 999 沒有現役人員
        $result = $this->validationService->validateCenterDeletion(999);

        // 由於是測試環境，可能會返回中心不存在的錯誤
        $this->assertArrayHasKey('can_delete', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('active_staff_count', $result);
    }

    /** @test */
    public function it_sanitizes_input_data()
    {
        $dirtyData = [
            'name' => '  測試中心  ',
            'description' => '<script>alert("xss")</script>',
            'nested' => [
                'field' => '  值  '
            ]
        ];

        $cleanData = $this->validationService->sanitizeInput($dirtyData);

        $this->assertEquals('測試中心', $cleanData['name']);
        $this->assertStringNotContainsString('<script>', $cleanData['description']);
        $this->assertEquals('值', $cleanData['nested']['field']);
    }

    /** @test */
    public function it_formats_validation_errors_correctly()
    {
        try {
            $this->validationService->validateCenterData([
                'name' => '',
                'center_level_id' => 'invalid'
            ]);
        } catch (ValidationException $e) {
            $formatted = $this->validationService->formatValidationErrors($e->errors());

            $this->assertIsArray($formatted);
            $this->assertArrayHasKey('name', $formatted);
            $this->assertArrayHasKey('center_level_id', $formatted);

            foreach ($formatted as $field => $error) {
                $this->assertArrayHasKey('field', $error);
                $this->assertArrayHasKey('messages', $error);
                $this->assertArrayHasKey('first_message', $error);
                $this->assertArrayHasKey('count', $error);
            }
        }
    }

    /** @test */
    public function it_logs_validation_failure()
    {
        $this->expectsEvents(\Illuminate\Log\Events\MessageLogged::class);

        try {
            $this->validationService->validateCenterData([
                'name' => '',
                'center_level_id' => 'invalid'
            ]);
        } catch (ValidationException $e) {
            $this->validationService->logValidationFailure(
                '測試操作',
                ['name' => '', 'center_level_id' => 'invalid'],
                $e->errors()
            );
        }
    }

    /** @test */
    public function it_validates_center_name_with_unicode_characters()
    {
        $validNames = [
            '台北中心',
            'Tokyo Center',
            '中心-分部',
            '中心_001',
            '中心（總部）',
            'Center (Main)',
            '測試中心 123'
        ];

        foreach ($validNames as $name) {
            $validData = [
                'name' => $name,
                'center_level_id' => 1,
                'status' => 1
            ];

            try {
                $result = $this->validationService->validateCenterData($validData);
                $this->assertEquals($name, $result['name']);
            } catch (ValidationException $e) {
                $this->fail("應該接受有效的中心名稱：{$name}，錯誤：" . $e->getMessage());
            }
        }
    }

    /** @test */
    public function it_validates_center_name_minimum_length()
    {
        $validData = [
            'name' => '中心', // 正好2個字元
            'center_level_id' => 1,
            'status' => 1
        ];

        $result = $this->validationService->validateCenterData($validData);
        $this->assertEquals('中心', $result['name']);

        // 測試1個字元的情況
        $invalidData = [
            'name' => '中',
            'center_level_id' => 1,
            'status' => 1
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateCenterData($invalidData);
    }

    /** @test */
    public function it_validates_role_assignment_business_rules()
    {
        // 測試會員不存在的情況
        $invalidData = [
            'center_id' => 1,
            'account_id' => 99999, // 不存在的會員ID
            'role_code' => 'director'
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateRoleAssignmentData($invalidData);
    }

    /** @test */
    public function it_validates_search_query_length_limits()
    {
        // 測試最小長度
        $validData = [
            'query' => 'ab', // 正好2個字元
            'limit' => 10
        ];

        $result = $this->validationService->validateAccountSearchData($validData);
        $this->assertEquals('ab', $result['query']);

        // 測試最大長度
        $validData = [
            'query' => str_repeat('a', 50), // 正好50個字元
            'limit' => 10
        ];

        $result = $this->validationService->validateAccountSearchData($validData);
        $this->assertEquals(str_repeat('a', 50), $result['query']);

        // 測試超過最大長度
        $invalidData = [
            'query' => str_repeat('a', 51), // 超過50個字元
            'limit' => 10
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateAccountSearchData($invalidData);
    }

    /** @test */
    public function it_validates_search_limit_boundaries()
    {
        // 測試最小限制
        $validData = [
            'query' => 'test',
            'limit' => 1
        ];

        $result = $this->validationService->validateAccountSearchData($validData);
        $this->assertEquals(1, $result['limit']);

        // 測試最大限制
        $validData = [
            'query' => 'test',
            'limit' => 50
        ];

        $result = $this->validationService->validateAccountSearchData($validData);
        $this->assertEquals(50, $result['limit']);

        // 測試超過最大限制
        $invalidData = [
            'query' => 'test',
            'limit' => 51
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateAccountSearchData($invalidData);

        // 測試低於最小限制
        $invalidData = [
            'query' => 'test',
            'limit' => 0
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateAccountSearchData($invalidData);
    }

    /** @test */
    public function it_validates_role_assignment_note_length()
    {
        // 測試有效的備註
        $validData = [
            'center_id' => 1,
            'account_id' => 1,
            'role_code' => 'director',
            'note' => '這是一個有效的備註'
        ];

        $result = $this->validationService->validateRoleAssignmentData($validData);
        $this->assertEquals('這是一個有效的備註', $result['note']);

        // 測試超長備註
        $invalidData = [
            'center_id' => 1,
            'account_id' => 1,
            'role_code' => 'director',
            'note' => str_repeat('很長的備註', 50) // 超過255字元
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validateRoleAssignmentData($invalidData);
    }

    /** @test */
    public function it_handles_edge_cases_in_sanitization()
    {
        $edgeCases = [
            'empty_string' => '',
            'only_spaces' => '   ',
            'mixed_spaces' => '  test  value  ',
            'special_chars' => '<>&"\'',
            'null_value' => null,
            'numeric_string' => '123',
            'boolean_true' => true,
            'boolean_false' => false
        ];

        $sanitized = $this->validationService->sanitizeInput($edgeCases);

        $this->assertEquals('', $sanitized['empty_string']);
        $this->assertEquals('', $sanitized['only_spaces']);
        $this->assertEquals('test value', $sanitized['mixed_spaces']);
        $this->assertStringContainsString('&lt;', $sanitized['special_chars']);
        $this->assertNull($sanitized['null_value']);
        $this->assertEquals('123', $sanitized['numeric_string']);
        $this->assertTrue($sanitized['boolean_true']);
        $this->assertFalse($sanitized['boolean_false']);
    }

    /** @test */
    public function it_validates_all_role_codes()
    {
        $validRoleCodes = [
            'founder',
            'director',
            'executive_director',
            'lecturer',
            'market',
            'sales'
        ];

        foreach ($validRoleCodes as $roleCode) {
            $validData = [
                'center_id' => 1,
                'account_id' => 1,
                'role_code' => $roleCode
            ];

            try {
                $result = $this->validationService->validateRoleAssignmentData($validData);
                $this->assertEquals($roleCode, $result['role_code']);
            } catch (ValidationException $e) {
                $this->fail("應該接受有效的角色代碼：{$roleCode}，錯誤：" . $e->getMessage());
            }
        }
    }

    /** @test */
    public function it_validates_center_status_values()
    {
        $validStatuses = [0, 1, '0', '1'];

        foreach ($validStatuses as $status) {
            $validData = [
                'name' => '測試中心',
                'center_level_id' => 1,
                'status' => $status
            ];

            try {
                $result = $this->validationService->validateCenterData($validData);
                $this->assertContains((int)$result['status'], [0, 1]);
            } catch (ValidationException $e) {
                $this->fail("應該接受有效的狀態值：{$status}，錯誤：" . $e->getMessage());
            }
        }

        // 測試無效狀態值
        $invalidStatuses = [2, -1, 'active', 'inactive'];

        foreach ($invalidStatuses as $status) {
            $invalidData = [
                'name' => '測試中心',
                'center_level_id' => 1,
                'status' => $status
            ];

            try {
                $this->validationService->validateCenterData($invalidData);
                $this->fail("應該拒絕無效的狀態值：{$status}");
            } catch (ValidationException $e) {
                $this->assertTrue(true); // 驗證通過
            }
        }
    }
}
