<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterStaff;
use App\Models\Main\CenterRole;
use App\Models\Main\Account;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * 模型關聯測試
 *
 * 測試需求：
 * - 驗證 Center 模型與其他模型的關聯關係
 * - 驗證 CenterStaff 模型的關聯關係
 * - 驗證模型的 scope 方法
 * - 驗證模型的輔助方法
 */
class CenterModelRelationshipsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function center_belongs_to_center_level()
    {
        $center = new Center();
        $relation = $center->level();

        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals('center_level_id', $relation->getForeignKeyName());
        $this->assertEquals('id', $relation->getOwnerKeyName());
    }

    /** @test */
    public function center_has_many_staff()
    {
        $center = new Center();
        $relation = $center->staff();

        $this->assertInstanceOf(HasMany::class, $relation);
        $this->assertEquals('center_id', $relation->getForeignKeyName());
    }

    /** @test */
    public function center_has_many_active_staff()
    {
        $center = new Center();
        $relation = $center->activeStaff();

        $this->assertInstanceOf(HasMany::class, $relation);
        $this->assertEquals('center_id', $relation->getForeignKeyName());
    }

    /** @test */
    public function center_belongs_to_many_roles()
    {
        $center = new Center();
        $relation = $center->roles();

        $this->assertInstanceOf(BelongsToMany::class, $relation);
        $this->assertEquals('center_staff', $relation->getTable());
        $this->assertEquals('center_id', $relation->getForeignPivotKeyName());
        $this->assertEquals('role_id', $relation->getRelatedPivotKeyName());
    }

    /** @test */
    public function center_staff_belongs_to_role()
    {
        $staff = new CenterStaff();
        $relation = $staff->role();

        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals('role_id', $relation->getForeignKeyName());
    }

    /** @test */
    public function center_staff_belongs_to_center()
    {
        $staff = new CenterStaff();
        $relation = $staff->center();

        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals('center_id', $relation->getForeignKeyName());
    }

    /** @test */
    public function center_staff_belongs_to_account()
    {
        $staff = new CenterStaff();
        $relation = $staff->account();

        $this->assertInstanceOf(BelongsTo::class, $relation);
        $this->assertEquals('account_id', $relation->getForeignKeyName());
    }

    /** @test */
    public function center_has_active_scope()
    {
        $query = Center::active();

        // Get the query builder to check the where clause
        $queryBuilder = $query->getQuery();
        $wheres = $queryBuilder->wheres;

        $this->assertCount(1, $wheres);
        $this->assertEquals('status', $wheres[0]['column']);
        $this->assertEquals('=', $wheres[0]['operator']);
        $this->assertEquals(1, $wheres[0]['value']);
    }

    /** @test */
    public function center_has_with_role_scope()
    {
        $query = Center::withRole('director');

        // Check that the query has a whereHas clause
        $queryBuilder = $query->getQuery();
        $this->assertNotEmpty($queryBuilder->wheres);
    }

    /** @test */
    public function center_has_with_account_role_scope()
    {
        $query = Center::withAccountRole(1, 'director');

        // Check that the query has a whereHas clause
        $queryBuilder = $query->getQuery();
        $this->assertNotEmpty($queryBuilder->wheres);
    }

    /** @test */
    public function center_staff_has_active_scope()
    {
        $query = CenterStaff::active();

        // Get the query builder to check the where clauses
        $queryBuilder = $query->getQuery();
        $wheres = $queryBuilder->wheres;

        // Should have two where clauses: end_at IS NULL and deleted_at IS NULL
        $this->assertGreaterThanOrEqual(2, count($wheres));

        // Check for null conditions
        $nullColumns = [];
        foreach ($wheres as $where) {
            if ($where['type'] === 'Null') {
                $nullColumns[] = $where['column'];
            }
        }

        $this->assertContains('end_at', $nullColumns);
        $this->assertContains('deleted_at', $nullColumns);
    }

    /** @test */
    public function center_staff_has_for_center_scope()
    {
        $query = CenterStaff::forCenter(1);

        $queryBuilder = $query->getQuery();
        $wheres = $queryBuilder->wheres;

        $this->assertCount(1, $wheres);
        $this->assertEquals('center_id', $wheres[0]['column']);
        $this->assertEquals('=', $wheres[0]['operator']);
        $this->assertEquals(1, $wheres[0]['value']);
    }

    /** @test */
    public function center_staff_has_single_role_scope()
    {
        $query = CenterStaff::singleRole('director');

        // Check that the query has a whereHas clause for role
        $queryBuilder = $query->getQuery();
        $this->assertNotEmpty($queryBuilder->wheres);
    }

    /** @test */
    public function center_has_active_role_helper()
    {
        $center = new Center();

        // Test that the method exists
        $this->assertTrue(method_exists($center, 'hasActiveRole'));

        // The actual functionality would be tested with database data
        // For unit testing, we just verify the method signature
        $reflection = new \ReflectionMethod($center, 'hasActiveRole');
        $parameters = $reflection->getParameters();

        $this->assertCount(1, $parameters);
        $this->assertEquals('roleCode', $parameters[0]->getName());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
    }

    /** @test */
    public function center_staff_has_end_now_helper()
    {
        $staff = new CenterStaff();

        // Test that the method exists
        $this->assertTrue(method_exists($staff, 'endNow'));

        // Test return type
        $reflection = new \ReflectionMethod($staff, 'endNow');
        $returnType = $reflection->getReturnType();

        $this->assertEquals('self', $returnType->getName());
    }

    /** @test */
    public function center_staff_has_renew_helper()
    {
        $staff = new CenterStaff();

        // Test that the method exists
        $this->assertTrue(method_exists($staff, 'renew'));

        // Test parameters
        $reflection = new \ReflectionMethod($staff, 'renew');
        $parameters = $reflection->getParameters();

        $this->assertCount(1, $parameters);
        $this->assertEquals('newStart', $parameters[0]->getName());
        $this->assertTrue($parameters[0]->allowsNull());
    }

    /** @test */
    public function center_model_has_correct_fillable_fields()
    {
        $center = new Center();
        $guarded = $center->getGuarded();

        // Center uses guarded instead of fillable
        $this->assertContains('id', $guarded);
    }

    /** @test */
    public function center_staff_model_has_correct_fillable_fields()
    {
        $staff = new CenterStaff();
        $fillable = $staff->getFillable();

        $expectedFillable = [
            'center_id',
            'account_id',
            'role_id',
            'start_at',
            'end_at',
            'note'
        ];

        foreach ($expectedFillable as $field) {
            $this->assertContains($field, $fillable);
        }
    }

    /** @test */
    public function center_model_has_correct_casts()
    {
        $center = new Center();
        $casts = $center->getCasts();

        $expectedCasts = [
            'status' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];

        foreach ($expectedCasts as $field => $cast) {
            $this->assertArrayHasKey($field, $casts);
            $this->assertEquals($cast, $casts[$field]);
        }
    }

    /** @test */
    public function center_staff_model_has_correct_casts()
    {
        $staff = new CenterStaff();
        $casts = $staff->getCasts();

        $expectedCasts = [
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            'active_flag' => 'boolean',
        ];

        foreach ($expectedCasts as $field => $cast) {
            $this->assertArrayHasKey($field, $casts);
            $this->assertEquals($cast, $casts[$field]);
        }
    }

    /** @test */
    public function center_model_uses_correct_connection()
    {
        $center = new Center();
        $this->assertEquals('main_db', $center->getConnectionName());
    }

    /** @test */
    public function center_staff_model_uses_correct_connection()
    {
        $staff = new CenterStaff();
        $this->assertEquals('main_db', $staff->getConnectionName());
    }

    /** @test */
    public function center_model_uses_correct_table()
    {
        $center = new Center();
        $this->assertEquals('centers', $center->getTable());
    }

    /** @test */
    public function center_staff_model_uses_correct_table()
    {
        $staff = new CenterStaff();
        $this->assertEquals('center_staff', $staff->getTable());
    }

    /** @test */
    public function center_staff_uses_soft_deletes()
    {
        $staff = new CenterStaff();
        $traits = class_uses($staff);

        $this->assertContains('Illuminate\Database\Eloquent\SoftDeletes', $traits);
    }

    /** @test */
    public function models_can_be_instantiated()
    {
        $center = new Center();
        $staff = new CenterStaff();
        $level = new CenterLevel();
        $role = new CenterRole();
        $account = new Account();

        $this->assertInstanceOf(Center::class, $center);
        $this->assertInstanceOf(CenterStaff::class, $staff);
        $this->assertInstanceOf(CenterLevel::class, $level);
        $this->assertInstanceOf(CenterRole::class, $role);
        $this->assertInstanceOf(Account::class, $account);
    }

    /** @test */
    public function center_roles_pivot_has_correct_columns()
    {
        $center = new Center();
        $relation = $center->roles();

        $pivotColumns = $relation->getPivotColumns();

        $expectedColumns = ['id', 'account_id', 'start_at', 'end_at', 'note', 'active_flag', 'deleted_at'];

        foreach ($expectedColumns as $column) {
            $this->assertContains($column, $pivotColumns);
        }
    }

    /** @test */
    public function center_roles_pivot_has_timestamps()
    {
        $center = new Center();
        $relation = $center->roles();

        // Check if withTimestamps is called
        $this->assertTrue($relation->withTimestamps);
    }

    /** @test */
    public function center_model_has_proper_query_scopes()
    {
        // Test that all expected scopes exist
        $center = new Center();

        $this->assertTrue(method_exists($center, 'scopeActive'));
        $this->assertTrue(method_exists($center, 'scopeWithRole'));
        $this->assertTrue(method_exists($center, 'scopeWithAccountRole'));
        $this->assertTrue(method_exists($center, 'scopeByLevel'));
        $this->assertTrue(method_exists($center, 'scopeWithStaffCount'));
    }

    /** @test */
    public function center_staff_model_has_proper_query_scopes()
    {
        // Test that all expected scopes exist
        $staff = new CenterStaff();

        $this->assertTrue(method_exists($staff, 'scopeActive'));
        $this->assertTrue(method_exists($staff, 'scopeForCenter'));
        $this->assertTrue(method_exists($staff, 'scopeSingleRole'));
        $this->assertTrue(method_exists($staff, 'scopeForAccount'));
        $this->assertTrue(method_exists($staff, 'scopeInDateRange'));
    }

    /** @test */
    public function center_model_has_helper_methods()
    {
        $center = new Center();

        // Test that helper methods exist
        $this->assertTrue(method_exists($center, 'hasActiveRole'));
        $this->assertTrue(method_exists($center, 'getActiveStaffForRole'));
        $this->assertTrue(method_exists($center, 'canBeDeleted'));
        $this->assertTrue(method_exists($center, 'getStaffHistory'));
    }

    /** @test */
    public function center_staff_model_has_helper_methods()
    {
        $staff = new CenterStaff();

        // Test that helper methods exist
        $this->assertTrue(method_exists($staff, 'endNow'));
        $this->assertTrue(method_exists($staff, 'renew'));
        $this->assertTrue(method_exists($staff, 'isActive'));
        $this->assertTrue(method_exists($staff, 'getDuration'));
    }

    /** @test */
    public function center_model_validates_relationships_integrity()
    {
        $center = new Center();

        // Test relationship method signatures
        $levelRelation = $center->level();
        $this->assertEquals('center_level_id', $levelRelation->getForeignKeyName());
        $this->assertEquals('id', $levelRelation->getOwnerKeyName());

        $staffRelation = $center->staff();
        $this->assertEquals('center_id', $staffRelation->getForeignKeyName());
        $this->assertEquals('id', $staffRelation->getLocalKeyName());
    }

    /** @test */
    public function center_staff_validates_relationships_integrity()
    {
        $staff = new CenterStaff();

        // Test all relationship foreign keys
        $centerRelation = $staff->center();
        $this->assertEquals('center_id', $centerRelation->getForeignKeyName());

        $accountRelation = $staff->account();
        $this->assertEquals('account_id', $accountRelation->getForeignKeyName());

        $roleRelation = $staff->role();
        $this->assertEquals('role_id', $roleRelation->getForeignKeyName());
    }

    /** @test */
    public function models_have_proper_attribute_casting()
    {
        $center = new Center();
        $staff = new CenterStaff();

        // Test Center casts
        $centerCasts = $center->getCasts();
        $this->assertArrayHasKey('status', $centerCasts);
        $this->assertEquals('integer', $centerCasts['status']);

        // Test CenterStaff casts
        $staffCasts = $staff->getCasts();
        $this->assertArrayHasKey('start_at', $staffCasts);
        $this->assertArrayHasKey('end_at', $staffCasts);
        $this->assertArrayHasKey('active_flag', $staffCasts);
        $this->assertEquals('datetime', $staffCasts['start_at']);
        $this->assertEquals('datetime', $staffCasts['end_at']);
        $this->assertEquals('boolean', $staffCasts['active_flag']);
    }

    /** @test */
    public function models_use_correct_database_configuration()
    {
        $center = new Center();
        $staff = new CenterStaff();
        $level = new CenterLevel();
        $role = new CenterRole();
        $account = new Account();

        // All models should use main_db connection
        $this->assertEquals('main_db', $center->getConnectionName());
        $this->assertEquals('main_db', $staff->getConnectionName());
        $this->assertEquals('main_db', $level->getConnectionName());
        $this->assertEquals('main_db', $role->getConnectionName());
        $this->assertEquals('main_db', $account->getConnectionName());

        // Test correct table names
        $this->assertEquals('centers', $center->getTable());
        $this->assertEquals('center_staff', $staff->getTable());
        $this->assertEquals('center_level', $level->getTable());
        $this->assertEquals('center_roles', $role->getTable());
        $this->assertEquals('account', $account->getTable());
    }

    /** @test */
    public function center_staff_soft_delete_configuration()
    {
        $staff = new CenterStaff();

        // Test soft delete trait
        $traits = class_uses_recursive($staff);
        $this->assertContains('Illuminate\Database\Eloquent\SoftDeletes', $traits);

        // Test deleted_at column in casts
        $casts = $staff->getCasts();
        $this->assertArrayHasKey('deleted_at', $casts);
        $this->assertEquals('datetime', $casts['deleted_at']);
    }

    /** @test */
    public function models_have_proper_primary_keys()
    {
        $center = new Center();
        $staff = new CenterStaff();
        $level = new CenterLevel();
        $role = new CenterRole();
        $account = new Account();

        // Test primary key configuration
        $this->assertEquals('id', $center->getKeyName());
        $this->assertEquals('id', $staff->getKeyName());
        $this->assertEquals('id', $level->getKeyName());
        $this->assertEquals('id', $role->getKeyName());
        $this->assertEquals('id', $account->getKeyName());

        // Test key type
        $this->assertEquals('int', $center->getKeyType());
        $this->assertEquals('int', $staff->getKeyType());
        $this->assertEquals('int', $level->getKeyType());
        $this->assertEquals('int', $role->getKeyType());
        $this->assertEquals('int', $account->getKeyType());
    }

    /** @test */
    public function center_model_has_proper_fillable_or_guarded()
    {
        $center = new Center();

        // Center should have either fillable or guarded properly configured
        $fillable = $center->getFillable();
        $guarded = $center->getGuarded();

        // Either fillable should be set or guarded should protect sensitive fields
        $this->assertTrue(
            !empty($fillable) || in_array('id', $guarded),
            'Center model should have proper mass assignment protection'
        );
    }

    /** @test */
    public function center_staff_model_has_proper_fillable()
    {
        $staff = new CenterStaff();
        $fillable = $staff->getFillable();

        $expectedFillable = [
            'center_id',
            'account_id',
            'role_id',
            'start_at',
            'end_at',
            'note'
        ];

        foreach ($expectedFillable as $field) {
            $this->assertContains($field, $fillable, "CenterStaff should have {$field} in fillable");
        }

        // Should not have id in fillable
        $this->assertNotContains('id', $fillable);
    }
}
