<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\Account;
use App\Services\Center\CenterService;
use App\Services\Center\CenterCacheService;

/**
 * 中心管理效能整合測試
 *
 * 測試效能優化功能的整合性和正確性
 */
class CenterPerformanceIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $centerService;
    protected $cacheService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerService = new CenterService();
        $this->cacheService = new CenterCacheService();

        // 清除快取
        Cache::flush();

        // 建立測試資料
        $this->createTestData();
    }

    protected function createTestData(): void
    {
        // 建立中心等級
        DB::connection('main_db')->table('center_level')->insert([
            ['id' => 1, 'name' => '一級中心', 'status' => 1],
            ['id' => 2, 'name' => '二級中心', 'status' => 1],
            ['id' => 3, 'name' => '三級中心', 'status' => 1],
        ]);

        // 建立角色
        DB::connection('main_db')->table('center_roles')->insert([
            ['id' => 1, 'code' => 'founder', 'name' => '中心發起人', 'is_singleton' => 1],
            ['id' => 2, 'code' => 'director', 'name' => '中心總監', 'is_singleton' => 1],
            ['id' => 3, 'code' => 'lecturer', 'name' => '講師', 'is_singleton' => 0],
        ]);

        // 建立測試會員
        for ($i = 1; $i <= 100; $i++) {
            DB::connection('main_db')->table('account')->insert([
                'id' => $i,
                'number' => sprintf('A%04d', $i),
                'name' => "測試會員{$i}",
                'email' => "test{$i}@example.com",
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // 建立測試中心
        for ($i = 1; $i <= 50; $i++) {
            DB::connection('main_db')->table('centers')->insert([
                'id' => $i,
                'name' => "測試中心{$i}",
                'center_level_id' => ($i % 3) + 1,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * 測試快取服務基本功能
     */
    public function test_cache_service_basic_functionality()
    {
        // 測試中心等級快取
        $levels1 = $this->cacheService->getCenterLevels();
        $levels2 = $this->cacheService->getCenterLevels();

        $this->assertEquals($levels1, $levels2);
        $this->assertCount(3, $levels1);

        // 驗證快取是否生效
        $this->assertTrue(Cache::has('center_management:center_levels'));

        // 測試角色快取
        $roles1 = $this->cacheService->getCenterRoles();
        $roles2 = $this->cacheService->getCenterRoles();

        $this->assertEquals($roles1, $roles2);
        $this->assertCount(3, $roles1);

        // 驗證快取是否生效
        $this->assertTrue(Cache::has('center_management:center_roles'));
    }

    /**
     * 測試中心統計快取
     */
    public function test_center_stats_caching()
    {
        $centerId = 1;

        // 建立一些人員記錄
        DB::connection('main_db')->table('center_staff')->insert([
            [
                'center_id' => $centerId,
                'account_id' => 1,
                'role_code' => 'founder',
                'start_at' => now(),
                'end_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'center_id' => $centerId,
                'account_id' => 2,
                'role_code' => 'lecturer',
                'start_at' => now(),
                'end_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // 第一次查詢（建立快取）
        $stats1 = $this->cacheService->getCenterStats($centerId);

        // 第二次查詢（使用快取）
        $stats2 = $this->cacheService->getCenterStats($centerId);

        $this->assertEquals($stats1, $stats2);
        $this->assertEquals(2, $stats1['active_staff_count']);
        $this->assertEquals(2, $stats1['total_staff_count']);

        // 驗證快取是否生效
        $this->assertTrue(Cache::has("center_management:center_stats:{$centerId}"));
    }

    /**
     * 測試快取清除功能
     */
    public function test_cache_clearing()
    {
        $centerId = 1;

        // 建立快取
        $this->cacheService->getCenterLevels();
        $this->cacheService->getCenterRoles();
        $this->cacheService->getCenterStats($centerId);

        // 驗證快取存在
        $this->assertTrue(Cache::has('center_management:center_levels'));
        $this->assertTrue(Cache::has('center_management:center_roles'));
        $this->assertTrue(Cache::has("center_management:center_stats:{$centerId}"));

        // 清除中心特定快取
        $this->cacheService->clearCenterCache($centerId);

        // 驗證中心快取已清除，但基礎資料快取仍存在
        $this->assertFalse(Cache::has("center_management:center_stats:{$centerId}"));
        $this->assertTrue(Cache::has('center_management:center_levels'));
        $this->assertTrue(Cache::has('center_management:center_roles'));

        // 清除基礎資料快取
        $this->cacheService->clearMasterDataCache();

        // 驗證基礎資料快取已清除
        $this->assertFalse(Cache::has('center_management:center_levels'));
        $this->assertFalse(Cache::has('center_management:center_roles'));
    }

    /**
     * 測試優化後的中心列表查詢
     */
    public function test_optimized_center_list_query()
    {
        // 測試無篩選條件
        $centers = $this->centerService->getAllCenters();
        $this->assertNotNull($centers);
        $this->assertGreaterThan(0, $centers->count());

        // 測試名稱篩選
        $centers = $this->centerService->getAllCenters(['name' => '測試中心1']);
        $this->assertNotNull($centers);

        // 測試等級篩選
        $centers = $this->centerService->getAllCenters(['center_level_id' => 1]);
        $this->assertNotNull($centers);

        // 測試狀態篩選
        $centers = $this->centerService->getAllCenters(['status' => 1]);
        $this->assertNotNull($centers);

        // 測試複合篩選
        $centers = $this->centerService->getAllCenters([
            'name' => '測試',
            'center_level_id' => 1,
            'status' => 1
        ]);
        $this->assertNotNull($centers);
    }

    /**
     * 測試優化後的會員搜尋功能
     */
    public function test_optimized_account_search()
    {
        // 測試前綴搜尋
        $accounts = $this->centerService->searchAccounts('A001');
        $this->assertNotNull($accounts);
        $this->assertGreaterThan(0, $accounts->count());

        // 測試部分匹配
        $accounts = $this->centerService->searchAccounts('A00');
        $this->assertNotNull($accounts);
        $this->assertGreaterThan(0, $accounts->count());

        // 測試限制結果數量
        $accounts = $this->centerService->searchAccounts('A', 5);
        $this->assertNotNull($accounts);
        $this->assertLessThanOrEqual(5, $accounts->count());

        // 測試空查詢
        $accounts = $this->centerService->searchAccounts('');
        $this->assertEquals(0, $accounts->count());

        // 測試短查詢
        $accounts = $this->centerService->searchAccounts('A');
        $this->assertNotNull($accounts);
    }

    /**
     * 測試快取在 CRUD 操作中的自動清除
     */
    public function test_cache_auto_clearing_on_crud()
    {
        // 建立快取
        $this->cacheService->getCenterLevels();
        $levels = $this->cacheService->getCenterStats(1);

        // 驗證快取存在
        $this->assertTrue(Cache::has('center_management:center_levels'));
        $this->assertTrue(Cache::has('center_management:center_stats:1'));

        // 建立新中心（應該清除列表快取）
        $centerData = [
            'name' => '新測試中心',
            'center_level_id' => 1,
            'status' => 1
        ];

        $center = $this->centerService->createCenter($centerData);
        $this->assertNotNull($center);

        // 更新中心（應該清除相關快取）
        $updateData = [
            'name' => '更新後的中心',
            'center_level_id' => 2,
            'status' => 1
        ];

        $updatedCenter = $this->centerService->updateCenter($center->id, $updateData);
        $this->assertEquals('更新後的中心', $updatedCenter->name);

        // 刪除中心（應該清除相關快取）
        $deleted = $this->centerService->deleteCenter($center->id);
        $this->assertTrue($deleted);
    }

    /**
     * 測試效能指標
     */
    public function test_performance_metrics()
    {
        $startTime = microtime(true);

        // 執行多次查詢測試
        for ($i = 0; $i < 10; $i++) {
            $this->centerService->getAllCenters();
            $this->centerService->searchAccounts('A00', 10);
            $this->cacheService->getCenterLevels();
            $this->cacheService->getCenterRoles();
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 驗證執行時間在合理範圍內（應該很快，因為有快取）
        $this->assertLessThan(1.0, $executionTime, '效能測試失敗：執行時間過長');
    }

    /**
     * 測試併發快取操作
     */
    public function test_concurrent_cache_operations()
    {
        // 模擬併發操作
        $results = [];

        for ($i = 0; $i < 5; $i++) {
            $results[] = $this->cacheService->getCenterLevels();
        }

        // 驗證所有結果一致
        $firstResult = $results[0];
        foreach ($results as $result) {
            $this->assertEquals($firstResult, $result);
        }
    }

    /**
     * 測試快取統計功能
     */
    public function test_cache_statistics()
    {
        // 建立一些快取
        $this->cacheService->getCenterLevels();
        $this->cacheService->getCenterRoles();
        $this->cacheService->getCenterStats(1);
        $this->cacheService->getCenterStats(2);

        // 取得快取統計
        $stats = $this->cacheService->getCacheStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('center_levels', $stats);
        $this->assertArrayHasKey('center_roles', $stats);
        $this->assertArrayHasKey('cached_centers', $stats);

        $this->assertTrue($stats['center_levels']);
        $this->assertTrue($stats['center_roles']);
    }

    /**
     * 測試快取預熱功能
     */
    public function test_cache_warmup()
    {
        // 清除所有快取
        Cache::flush();

        // 驗證快取不存在
        $this->assertFalse(Cache::has('center_management:center_levels'));
        $this->assertFalse(Cache::has('center_management:center_roles'));

        // 執行快取預熱
        $this->cacheService->warmupCache();

        // 驗證快取已建立
        $this->assertTrue(Cache::has('center_management:center_levels'));
        $this->assertTrue(Cache::has('center_management:center_roles'));
    }

    /**
     * 測試資料庫索引效果
     */
    public function test_database_index_effectiveness()
    {
        // 這個測試需要實際的資料庫環境來驗證索引效果
        // 在測試環境中，我們主要驗證查詢能正常執行

        $startTime = microtime(true);

        // 執行各種查詢
        DB::connection('main_db')->table('centers')
            ->where('name', 'like', '%測試%')
            ->where('status', 1)
            ->get();

        DB::connection('main_db')->table('centers')
            ->where('center_level_id', 1)
            ->where('status', 1)
            ->get();

        DB::connection('main_db')->table('center_staff')
            ->where('center_id', 1)
            ->whereNull('end_at')
            ->get();

        DB::connection('main_db')->table('account')
            ->where('number', 'like', 'A001%')
            ->where('status', 1)
            ->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 驗證查詢執行時間合理
        $this->assertLessThan(0.5, $executionTime, '資料庫查詢效能測試失敗');
    }

    /**
     * 測試記憶體使用情況
     */
    public function test_memory_usage()
    {
        $initialMemory = memory_get_usage();

        // 執行大量操作
        for ($i = 0; $i < 100; $i++) {
            $this->centerService->getAllCenters();
            $this->centerService->searchAccounts('A', 10);
        }

        $finalMemory = memory_get_usage();
        $memoryIncrease = $finalMemory - $initialMemory;

        // 驗證記憶體增長在合理範圍內（小於 10MB）
        $this->assertLessThan(10 * 1024 * 1024, $memoryIncrease, '記憶體使用量過高');
    }

    protected function tearDown(): void
    {
        // 清除快取
        Cache::flush();

        parent::tearDown();
    }
}
