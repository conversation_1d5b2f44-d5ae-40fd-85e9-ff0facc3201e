<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use App\Services\Center\CenterService;
use App\Services\Center\CenterStaffService;
use App\Http\Controllers\admin\Center as CenterController;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\QueryException;

/**
 * 中心管理錯誤處理流程整合測試
 *
 * 專門測試各種錯誤情況的處理流程和恢復機制
 * 涵蓋需求: 7.4
 */
class CenterErrorHandlingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $centerService;
    protected $centerStaffService;
    protected $centerController;
    protected $testData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerService = app(CenterService::class);
        $this->centerStaffService = app(CenterStaffService::class);
        $this->centerController = app(CenterController::class);

        $this->setupTestData();
    }

    private function setupTestData(): void
    {
        $this->testData = (object) [
            'centerLevel' => CenterLevel::create([
                'name' => '錯誤處理測試等級',
                'level' => 1,
                'status' => 1
            ]),
            'accounts' => collect(),
            'roles' => collect()
        ];

        // 建立測試帳號
        for ($i = 1; $i <= 5; $i++) {
            $account = Account::create([
                'number' => 'ERROR' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'name' => '錯誤測試會員' . $i,
                'email' => 'error' . $i . '@example.com',
                'status' => 1
            ]);
            $this->testData->accounts->push($account);
        }

        // 建立測試角色
        $roles = [
            ['code' => 'founder', 'name' => '中心發起人', 'is_singleton' => true],
            ['code' => 'director', 'name' => '中心總監', 'is_singleton' => true],
            ['code' => 'lecturer', 'name' => '講師', 'is_singleton' => false],
        ];

        foreach ($roles as $roleData) {
            $role = CenterRole::create($roleData);
            $this->testData->roles->put($roleData['code'], $role);
        }
    }

    /**
     * 測試驗證錯誤處理流程
     * 需求: 7.4
     */
    public function test_validation_error_handling_workflow(): void
    {
        // 測試 1: 空中心名稱驗證錯誤
        try {
            $this->centerService->createCenter([
                'name' => '', // 空名稱
                'center_level_id' => $this->testData->centerLevel->id,
                'status' => 1
            ]);
            $this->fail('應該拋出驗證異常');
        } catch (ValidationException $e) {
            $this->assertArrayHasKey('name', $e->errors());
            $this->assertStringContainsString('required', $e->errors()['name'][0]);
        } catch (Exception $e) {
            $this->assertStringContainsString('name', strtolower($e->getMessage()));
        }

        // 測試 2: 無效中心等級驗證錯誤
        try {
            $this->centerService->createCenter([
                'name' => '測試中心',
                'center_level_id' => 99999, // 不存在的等級
                'status' => 1
            ]);
            $this->fail('應該拋出驗證異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 3: 無效狀態值驗證錯誤
        try {
            $this->centerService->createCenter([
                'name' => '測試中心',
                'center_level_id' => $this->testData->centerLevel->id,
                'status' => 999 // 無效狀態
            ]);
            $this->fail('應該拋出驗證異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 4: 驗證錯誤後的系統狀態
        $centerCountBefore = Center::count();

        try {
            $this->centerService->createCenter([
                'name' => '',
                'center_level_id' => $this->testData->centerLevel->id,
                'status' => 1
            ]);
        } catch (Exception $e) {
            // 驗證失敗後不應該有新的中心記錄
            $centerCountAfter = Center::count();
            $this->assertEquals($centerCountBefore, $centerCountAfter, '驗證失敗後不應該建立新記錄');
        }
    }

    /**
     * 測試業務邏輯錯誤處理流程
     * 需求: 7.4
     */
    public function test_business_logic_error_handling_workflow(): void
    {
        $center = Center::create([
            'name' => '業務邏輯錯誤測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 測試 1: 單例角色衝突錯誤
        $founderRole = $this->testData->roles->get('founder');
        $account1 = $this->testData->accounts->get(0);
        $account2 = $this->testData->accounts->get(1);

        // 先指派第一個發起人
        $this->centerStaffService->assignRole(
            $center->id,
            $account1->id,
            $founderRole->id
        );

        // 嘗試指派第二個發起人（不自動結束現有的）
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $account2->id,
                $founderRole->id,
                false // 不自動結束現有角色
            );
            $this->fail('應該拋出單例角色衝突異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('singleton', strtolower($e->getMessage()));

            // 驗證原有角色仍然存在
            $activeFounders = CenterStaff::where('center_id', $center->id)
                ->where('center_role_id', $founderRole->id)
                ->whereNull('end_at')
                ->count();
            $this->assertEquals(1, $activeFounders, '原有發起人應該仍然存在');
        }

        // 測試 2: 刪除有現役人員的中心錯誤
        try {
            $this->centerService->deleteCenter($center->id);
            $this->fail('應該拋出無法刪除異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('active', strtolower($e->getMessage()));

            // 驗證中心仍然存在
            $this->assertNotNull(Center::find($center->id), '中心應該仍然存在');
        }

        // 測試 3: 不存在的帳號角色指派錯誤
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                99999, // 不存在的帳號
                $founderRole->id
            );
            $this->fail('應該拋出帳號不存在異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 4: 不存在的角色指派錯誤
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $account1->id,
                99999 // 不存在的角色
            );
            $this->fail('應該拋出角色不存在異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }
    }

    /**
     * 測試資料庫錯誤處理流程
     * 需求: 7.4
     */
    public function test_database_error_handling_workflow(): void
    {
        // 測試 1: 外鍵約束錯誤
        try {
            CenterStaff::create([
                'center_id' => 99999, // 不存在的中心
                'account_id' => $this->testData->accounts->first()->id,
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);
            $this->fail('應該拋出外鍵約束異常');
        } catch (QueryException $e) {
            $this->assertStringContainsString('foreign', strtolower($e->getMessage()));
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 2: 交易失敗和回滾
        $initialCenterCount = Center::count();
        $initialStaffCount = CenterStaff::count();

        try {
            DB::beginTransaction();

            // 成功建立中心
            $center = Center::create([
                'name' => '交易測試中心',
                'center_level_id' => $this->testData->centerLevel->id,
                'status' => 1
            ]);

            // 故意失敗的人員指派
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => 99999, // 不存在的帳號
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);

            DB::commit();
            $this->fail('交易應該失敗');
        } catch (Exception $e) {
            DB::rollback();

            // 驗證交易回滾
            $finalCenterCount = Center::count();
            $finalStaffCount = CenterStaff::count();

            $this->assertEquals($initialCenterCount, $finalCenterCount, '中心數量應該回滾');
            $this->assertEquals($initialStaffCount, $finalStaffCount, '人員數量應該回滾');
        }

        // 測試 3: 連線錯誤模擬
        $originalConfig = Config::get('database.connections.main_db');

        try {
            // 模擬資料庫連線錯誤
            Config::set('database.connections.main_db.host', 'invalid_host');
            DB::purge('main_db');

            $this->centerService->createCenter([
                'name' => '連線錯誤測試中心',
                'center_level_id' => $this->testData->centerLevel->id,
                'status' => 1
            ]);
            $this->fail('應該拋出連線錯誤');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        } finally {
            // 恢復原始配置
            Config::set('database.connections.main_db', $originalConfig);
            DB::purge('main_db');
        }
    }

    /**
     * 測試併發錯誤處理流程
     * 需求: 7.4
     */
    public function test_concurrent_error_handling_workflow(): void
    {
        $center = Center::create([
            'name' => '併發錯誤測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $founderRole = $this->testData->roles->get('founder');
        $account1 = $this->testData->accounts->get(0);
        $account2 = $this->testData->accounts->get(1);

        $errors = [];
        $successes = [];

        // 模擬併發單例角色指派錯誤
        $processes = [
            function () use ($center, $founderRole, $account1, &$errors, &$successes) {
                try {
                    $this->centerStaffService->assignRole(
                        $center->id,
                        $account1->id,
                        $founderRole->id,
                        false // 不自動結束，測試衝突
                    );
                    $successes[] = 'account1';
                } catch (Exception $e) {
                    $errors[] = [
                        'account' => 'account1',
                        'error' => $e->getMessage()
                    ];
                }
            },

            function () use ($center, $founderRole, $account2, &$errors, &$successes) {
                try {
                    // 稍微延遲
                    usleep(1000);

                    $this->centerStaffService->assignRole(
                        $center->id,
                        $account2->id,
                        $founderRole->id,
                        false // 不自動結束，測試衝突
                    );
                    $successes[] = 'account2';
                } catch (Exception $e) {
                    $errors[] = [
                        'account' => 'account2',
                        'error' => $e->getMessage()
                    ];
                }
            }
        ];

        // 執行併發程序
        foreach ($processes as $process) {
            $process();
        }

        // 驗證錯誤處理結果
        $this->assertEquals(1, count($successes), '應該有一個成功');
        $this->assertEquals(1, count($errors), '應該有一個錯誤');

        // 驗證系統狀態一致性
        $activeFounders = CenterStaff::where('center_id', $center->id)
            ->where('center_role_id', $founderRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(1, $activeFounders, '應該只有一個現役發起人');
    }

    /**
     * 測試錯誤恢復和重試機制
     * 需求: 7.4
     */
    public function test_error_recovery_and_retry_mechanism(): void
    {
        $center = Center::create([
            'name' => '錯誤恢復測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $founderRole = $this->testData->roles->get('founder');
        $account = $this->testData->accounts->first();

        // 模擬暫時性錯誤和重試
        $maxRetries = 3;
        $attempt = 0;
        $success = false;

        while ($attempt < $maxRetries && !$success) {
            try {
                $attempt++;

                // 模擬間歇性錯誤
                if ($attempt < 2) {
                    throw new Exception("模擬暫時性錯誤 - 嘗試 {$attempt}");
                }

                // 第二次嘗試成功
                $this->centerStaffService->assignRole(
                    $center->id,
                    $account->id,
                    $founderRole->id
                );

                $success = true;
            } catch (Exception $e) {
                Log::warning("角色指派嘗試 {$attempt} 失敗", [
                    'error' => $e->getMessage(),
                    'center_id' => $center->id,
                    'account_id' => $account->id
                ]);

                if ($attempt >= $maxRetries) {
                    throw $e;
                }

                // 重試前等待
                usleep(100000); // 0.1秒
            }
        }

        $this->assertTrue($success, '重試機制應該最終成功');
        $this->assertEquals(2, $attempt, '應該在第2次嘗試成功');

        // 驗證最終狀態
        $activeStaff = CenterStaff::where('center_id', $center->id)
            ->where('account_id', $account->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(1, $activeStaff, '應該有一個現役人員');
    }

    /**
     * 測試複雜錯誤場景處理
     * 需求: 7.4
     */
    public function test_complex_error_scenario_handling(): void
    {
        // 場景：在複雜的業務流程中發生多種錯誤
        $center = Center::create([
            'name' => '複雜錯誤場景測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $founderRole = $this->testData->roles->get('founder');
        $directorRole = $this->testData->roles->get('director');
        $lecturerRole = $this->testData->roles->get('lecturer');

        $errorLog = [];
        $successLog = [];

        // 步驟 1: 正常指派發起人
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $this->testData->accounts->get(0)->id,
                $founderRole->id
            );
            $successLog[] = 'founder_assigned';
        } catch (Exception $e) {
            $errorLog[] = ['step' => 'founder_assignment', 'error' => $e->getMessage()];
        }

        // 步驟 2: 嘗試指派不存在的總監（應該失敗）
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                99999, // 不存在的帳號
                $directorRole->id
            );
            $successLog[] = 'director_assigned';
        } catch (Exception $e) {
            $errorLog[] = ['step' => 'director_assignment', 'error' => $e->getMessage()];
        }

        // 步驟 3: 正常指派講師
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $this->testData->accounts->get(1)->id,
                $lecturerRole->id
            );
            $successLog[] = 'lecturer_assigned';
        } catch (Exception $e) {
            $errorLog[] = ['step' => 'lecturer_assignment', 'error' => $e->getMessage()];
        }

        // 步驟 4: 嘗試刪除有現役人員的中心（應該失敗）
        try {
            $this->centerService->deleteCenter($center->id);
            $successLog[] = 'center_deleted';
        } catch (Exception $e) {
            $errorLog[] = ['step' => 'center_deletion', 'error' => $e->getMessage()];
        }

        // 驗證錯誤處理結果
        $this->assertCount(2, $successLog, '應該有2個成功操作');
        $this->assertCount(2, $errorLog, '應該有2個錯誤操作');

        // 驗證系統狀態一致性
        $this->assertNotNull(Center::find($center->id), '中心應該仍然存在');

        $activeStaff = CenterStaff::where('center_id', $center->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(2, $activeStaff, '應該有2個現役人員（發起人和講師）');

        // 驗證特定角色狀態
        $activeFounders = CenterStaff::where('center_id', $center->id)
            ->where('center_role_id', $founderRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(1, $activeFounders, '應該有1個現役發起人');

        $activeDirectors = CenterStaff::where('center_id', $center->id)
            ->where('center_role_id', $directorRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(0, $activeDirectors, '應該沒有現役總監');

        $activeLecturers = CenterStaff::where('center_id', $center->id)
            ->where('center_role_id', $lecturerRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(1, $activeLecturers, '應該有1個現役講師');
    }

    /**
     * 測試錯誤日誌和監控
     * 需求: 7.4
     */
    public function test_error_logging_and_monitoring(): void
    {
        // 清除之前的日誌
        Log::getLogger()->reset();

        $center = Center::create([
            'name' => '錯誤日誌測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 觸發各種錯誤並檢查日誌
        $errorScenarios = [
            [
                'name' => 'validation_error',
                'action' => function () {
                    $this->centerService->createCenter([
                        'name' => '',
                        'center_level_id' => $this->testData->centerLevel->id,
                        'status' => 1
                    ]);
                }
            ],
            [
                'name' => 'business_logic_error',
                'action' => function () use ($center) {
                    $this->centerService->deleteCenter($center->id);
                }
            ],
            [
                'name' => 'database_error',
                'action' => function () use ($center) {
                    CenterStaff::create([
                        'center_id' => $center->id,
                        'account_id' => 99999,
                        'center_role_id' => $this->testData->roles->get('founder')->id,
                        'start_at' => now(),
                    ]);
                }
            ]
        ];

        $loggedErrors = [];

        foreach ($errorScenarios as $scenario) {
            try {
                $scenario['action']();
            } catch (Exception $e) {
                $loggedErrors[] = [
                    'scenario' => $scenario['name'],
                    'error' => $e->getMessage(),
                    'timestamp' => now()
                ];

                // 記錄錯誤到日誌
                Log::error("中心管理錯誤: {$scenario['name']}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        // 驗證錯誤都被捕獲和記錄
        $this->assertCount(3, $loggedErrors, '應該記錄3個錯誤');

        // 驗證錯誤類型
        $errorTypes = array_column($loggedErrors, 'scenario');
        $this->assertContains('validation_error', $errorTypes);
        $this->assertContains('business_logic_error', $errorTypes);
        $this->assertContains('database_error', $errorTypes);
    }

    /**
     * 測試用戶友善錯誤訊息
     * 需求: 7.4
     */
    public function test_user_friendly_error_messages(): void
    {
        // 測試各種錯誤的用戶友善訊息
        $errorTests = [
            [
                'scenario' => '空中心名稱',
                'action' => function () {
                    $this->centerService->createCenter([
                        'name' => '',
                        'center_level_id' => $this->testData->centerLevel->id,
                        'status' => 1
                    ]);
                },
                'expected_message_contains' => ['name', 'required', '必填']
            ],
            [
                'scenario' => '不存在的中心等級',
                'action' => function () {
                    $this->centerService->createCenter([
                        'name' => '測試中心',
                        'center_level_id' => 99999,
                        'status' => 1
                    ]);
                },
                'expected_message_contains' => ['level', 'exist', '不存在']
            ]
        ];

        foreach ($errorTests as $test) {
            try {
                $test['action']();
                $this->fail("測試 '{$test['scenario']}' 應該拋出異常");
            } catch (Exception $e) {
                $errorMessage = strtolower($e->getMessage());

                // 檢查錯誤訊息是否包含預期的關鍵字
                $containsExpectedKeyword = false;
                foreach ($test['expected_message_contains'] as $keyword) {
                    if (strpos($errorMessage, strtolower($keyword)) !== false) {
                        $containsExpectedKeyword = true;
                        break;
                    }
                }

                $this->assertTrue(
                    $containsExpectedKeyword,
                    "錯誤訊息 '{$e->getMessage()}' 應該包含預期關鍵字之一: " .
                        implode(', ', $test['expected_message_contains'])
                );
            }
        }
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        CenterStaff::truncate();
        Center::truncate();
        CenterRole::truncate();
        CenterLevel::truncate();
        Account::truncate();

        parent::tearDown();
    }
}
