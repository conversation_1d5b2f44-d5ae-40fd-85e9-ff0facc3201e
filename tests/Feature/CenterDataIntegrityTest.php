<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use App\Services\Center\CenterService;
use App\Services\Center\CenterStaffService;
use Exception;

/**
 * 中心管理資料完整性整合測試
 *
 * 專門測試資料完整性約束、外鍵關聯和資料一致性
 * 涵蓋需求: 7.3
 */
class CenterDataIntegrityTest extends TestCase
{
    use RefreshDatabase;

    protected $centerService;
    protected $centerStaffService;
    protected $testData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerService = app(CenterService::class);
        $this->centerStaffService = app(CenterStaffService::class);
        $this->setupTestData();
    }

    private function setupTestData(): void
    {
        $this->testData = (object) [
            'centerLevel' => CenterLevel::create([
                'name' => '資料完整性測試等級',
                'level' => 1,
                'status' => 1
            ]),
            'accounts' => collect(),
            'roles' => collect(),
            'centers' => collect()
        ];

        // 建立測試帳號
        for ($i = 1; $i <= 10; $i++) {
            $account = Account::create([
                'number' => 'INTEG' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'name' => '完整性測試會員' . $i,
                'email' => 'integrity' . $i . '@example.com',
                'status' => 1
            ]);
            $this->testData->accounts->push($account);
        }

        // 建立測試角色
        $roles = [
            ['code' => 'founder', 'name' => '中心發起人', 'is_singleton' => true],
            ['code' => 'director', 'name' => '中心總監', 'is_singleton' => true],
            ['code' => 'lecturer', 'name' => '講師', 'is_singleton' => false],
        ];

        foreach ($roles as $roleData) {
            $role = CenterRole::create($roleData);
            $this->testData->roles->put($roleData['code'], $role);
        }
    }

    /**
     * 測試外鍵約束完整性
     * 需求: 7.3
     */
    public function test_foreign_key_constraints(): void
    {
        $center = Center::create([
            'name' => '外鍵測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 測試 1: 嘗試建立指向不存在帳號的人員記錄
        $this->expectException(Exception::class);

        try {
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => 99999, // 不存在的帳號ID
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);
            $this->fail('應該拋出外鍵約束異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('foreign', strtolower($e->getMessage()));
        }

        // 測試 2: 嘗試建立指向不存在角色的人員記錄
        try {
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $this->testData->accounts->first()->id,
                'center_role_id' => 99999, // 不存在的角色ID
                'start_at' => now(),
            ]);
            $this->fail('應該拋出外鍵約束異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('foreign', strtolower($e->getMessage()));
        }

        // 測試 3: 嘗試建立指向不存在中心的人員記錄
        try {
            CenterStaff::create([
                'center_id' => 99999, // 不存在的中心ID
                'account_id' => $this->testData->accounts->first()->id,
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);
            $this->fail('應該拋出外鍵約束異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('foreign', strtolower($e->getMessage()));
        }
    }

    /**
     * 測試資料類型和約束完整性
     * 需求: 7.3
     */
    public function test_data_type_and_constraint_integrity(): void
    {
        $center = Center::create([
            'name' => '約束測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 測試 1: 必填欄位約束
        $this->expectException(Exception::class);

        try {
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $this->testData->accounts->first()->id,
                // 缺少 center_role_id
                'start_at' => now(),
            ]);
            $this->fail('應該拋出必填欄位異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 2: 日期格式約束
        $validStaff = CenterStaff::create([
            'center_id' => $center->id,
            'account_id' => $this->testData->accounts->first()->id,
            'center_role_id' => $this->testData->roles->get('founder')->id,
            'start_at' => now(),
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $validStaff->start_at);
        $this->assertNull($validStaff->end_at);

        // 測試 3: 狀態值約束
        $center->status = 1;
        $center->save();
        $this->assertEquals(1, $center->fresh()->status);

        $center->status = 0;
        $center->save();
        $this->assertEquals(0, $center->fresh()->status);
    }

    /**
     * 測試級聯刪除和資料完整性
     * 需求: 7.3
     */
    public function test_cascade_delete_integrity(): void
    {
        $center = Center::create([
            'name' => '級聯刪除測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 建立人員記錄
        $staff1 = CenterStaff::create([
            'center_id' => $center->id,
            'account_id' => $this->testData->accounts->get(0)->id,
            'center_role_id' => $this->testData->roles->get('founder')->id,
            'start_at' => now(),
        ]);

        $staff2 = CenterStaff::create([
            'center_id' => $center->id,
            'account_id' => $this->testData->accounts->get(1)->id,
            'center_role_id' => $this->testData->roles->get('lecturer')->id,
            'start_at' => now(),
        ]);

        // 驗證人員記錄存在
        $this->assertEquals(2, CenterStaff::where('center_id', $center->id)->count());

        // 刪除中心（如果配置了級聯刪除）
        $center->delete();

        // 檢查相關記錄的處理
        // 注意：實際行為取決於資料庫外鍵設定
        $remainingStaff = CenterStaff::where('center_id', $center->id)->count();

        // 如果配置了級聯刪除，應該為0；如果配置了限制刪除，上面的刪除操作應該失敗
        $this->assertTrue($remainingStaff >= 0, '人員記錄數量應該是有效的');
    }

    /**
     * 測試交易完整性
     * 需求: 7.3
     */
    public function test_transaction_integrity(): void
    {
        $center = Center::create([
            'name' => '交易完整性測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $initialStaffCount = CenterStaff::count();

        // 測試交易回滾
        try {
            DB::beginTransaction();

            // 成功的操作
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $this->testData->accounts->get(0)->id,
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);

            // 故意失敗的操作
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => 99999, // 不存在的帳號
                'center_role_id' => $this->testData->roles->get('lecturer')->id,
                'start_at' => now(),
            ]);

            DB::commit();
            $this->fail('交易應該失敗');
        } catch (Exception $e) {
            DB::rollback();

            // 驗證交易回滾後沒有新記錄
            $finalStaffCount = CenterStaff::count();
            $this->assertEquals($initialStaffCount, $finalStaffCount, '交易回滾後記錄數應該不變');
        }

        // 測試成功的交易
        DB::beginTransaction();

        try {
            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $this->testData->accounts->get(0)->id,
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);

            CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $this->testData->accounts->get(1)->id,
                'center_role_id' => $this->testData->roles->get('lecturer')->id,
                'start_at' => now(),
            ]);

            DB::commit();

            // 驗證成功交易後記錄增加
            $finalStaffCount = CenterStaff::count();
            $this->assertEquals($initialStaffCount + 2, $finalStaffCount, '成功交易後應該增加2條記錄');
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * 測試唯一性約束
     * 需求: 7.3
     */
    public function test_uniqueness_constraints(): void
    {
        $center = Center::create([
            'name' => '唯一性測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $account = $this->testData->accounts->first();
        $founderRole = $this->testData->roles->get('founder');

        // 建立第一個人員記錄
        $staff1 = CenterStaff::create([
            'center_id' => $center->id,
            'account_id' => $account->id,
            'center_role_id' => $founderRole->id,
            'start_at' => now(),
        ]);

        // 嘗試建立重複的現役人員記錄（如果有唯一性約束）
        try {
            $staff2 = CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $account->id,
                'center_role_id' => $founderRole->id,
                'start_at' => now(),
            ]);

            // 如果沒有唯一性約束，檢查業務邏輯是否處理重複
            $activeStaff = CenterStaff::where('center_id', $center->id)
                ->where('account_id', $account->id)
                ->where('center_role_id', $founderRole->id)
                ->whereNull('end_at')
                ->count();

            // 對於單例角色，應該只有一個現役記錄
            if ($founderRole->is_singleton) {
                $this->assertLessThanOrEqual(1, $activeStaff, '單例角色應該只有一個現役記錄');
            }
        } catch (Exception $e) {
            // 如果有唯一性約束，這是預期的行為
            $this->assertStringContainsString('unique', strtolower($e->getMessage()));
        }
    }

    /**
     * 測試資料一致性檢查
     * 需求: 7.3
     */
    public function test_data_consistency_checks(): void
    {
        $center = Center::create([
            'name' => '一致性測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 建立人員記錄
        $staff = CenterStaff::create([
            'center_id' => $center->id,
            'account_id' => $this->testData->accounts->first()->id,
            'center_role_id' => $this->testData->roles->get('founder')->id,
            'start_at' => now(),
        ]);

        // 測試 1: 開始時間不能晚於結束時間
        $staff->end_at = now()->subDay();

        try {
            $staff->save();

            // 檢查邏輯一致性
            $this->assertLessThanOrEqual($staff->end_at, $staff->start_at, '結束時間不能早於開始時間');
        } catch (Exception $e) {
            // 如果有資料庫約束檢查，這是預期的
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試 2: 狀態一致性
        $center->status = 0; // 停用中心
        $center->save();

        // 檢查相關業務邏輯是否一致
        $centerWithStaff = Center::with('activeStaff')->find($center->id);

        // 業務邏輯可能要求停用的中心不能有現役人員
        // 這取決於具體的業務規則實現
        $this->assertNotNull($centerWithStaff);
    }

    /**
     * 測試關聯資料完整性
     * 需求: 7.3
     */
    public function test_relationship_data_integrity(): void
    {
        $center = Center::create([
            'name' => '關聯完整性測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 建立多個人員記錄
        $staffRecords = [];
        foreach ($this->testData->accounts->take(3) as $index => $account) {
            $role = $index === 0 ? 'founder' : 'lecturer';
            $staffRecords[] = CenterStaff::create([
                'center_id' => $center->id,
                'account_id' => $account->id,
                'center_role_id' => $this->testData->roles->get($role)->id,
                'start_at' => now(),
            ]);
        }

        // 測試關聯查詢完整性
        $centerWithRelations = Center::with([
            'centerLevel',
            'activeStaff',
            'activeStaff.account',
            'activeStaff.centerRole'
        ])->find($center->id);

        // 驗證關聯資料存在且正確
        $this->assertNotNull($centerWithRelations->centerLevel);
        $this->assertEquals($this->testData->centerLevel->id, $centerWithRelations->centerLevel->id);

        $this->assertCount(3, $centerWithRelations->activeStaff);

        foreach ($centerWithRelations->activeStaff as $staff) {
            $this->assertNotNull($staff->account);
            $this->assertNotNull($staff->centerRole);
            $this->assertEquals($center->id, $staff->center_id);
            $this->assertNull($staff->end_at);
        }

        // 測試反向關聯
        foreach ($staffRecords as $staff) {
            $staffWithRelations = CenterStaff::with(['center', 'account', 'centerRole'])
                ->find($staff->id);

            $this->assertNotNull($staffWithRelations->center);
            $this->assertNotNull($staffWithRelations->account);
            $this->assertNotNull($staffWithRelations->centerRole);
            $this->assertEquals($center->id, $staffWithRelations->center->id);
        }
    }

    /**
     * 測試大量資料下的完整性
     * 需求: 7.3
     */
    public function test_bulk_data_integrity(): void
    {
        $center = Center::create([
            'name' => '大量資料完整性測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        $lecturerRole = $this->testData->roles->get('lecturer');
        $bulkData = [];

        // 準備大量資料
        for ($i = 0; $i < 100; $i++) {
            $account = $this->testData->accounts->get($i % $this->testData->accounts->count());
            $bulkData[] = [
                'center_id' => $center->id,
                'account_id' => $account->id,
                'center_role_id' => $lecturerRole->id,
                'start_at' => now()->addMinutes($i),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // 批量插入
        CenterStaff::insert($bulkData);

        // 驗證資料完整性
        $insertedCount = CenterStaff::where('center_id', $center->id)->count();
        $this->assertEquals(100, $insertedCount, '應該插入100條記錄');

        // 驗證每條記錄的完整性
        $allStaff = CenterStaff::where('center_id', $center->id)->get();

        foreach ($allStaff as $staff) {
            $this->assertNotNull($staff->center_id);
            $this->assertNotNull($staff->account_id);
            $this->assertNotNull($staff->center_role_id);
            $this->assertNotNull($staff->start_at);
            $this->assertNotNull($staff->created_at);
            $this->assertNotNull($staff->updated_at);

            // 驗證外鍵關聯存在
            $this->assertNotNull(Center::find($staff->center_id));
            $this->assertNotNull(Account::find($staff->account_id));
            $this->assertNotNull(CenterRole::find($staff->center_role_id));
        }

        // 驗證統計資料一致性
        $uniqueAccounts = CenterStaff::where('center_id', $center->id)
            ->distinct('account_id')
            ->count();
        $this->assertEquals($this->testData->accounts->count(), $uniqueAccounts);
    }

    /**
     * 測試資料庫約束和觸發器
     * 需求: 7.3
     */
    public function test_database_constraints_and_triggers(): void
    {
        // 檢查資料表結構
        $this->assertTrue(Schema::hasTable('centers'), 'centers 表應該存在');
        $this->assertTrue(Schema::hasTable('center_staff'), 'center_staff 表應該存在');
        $this->assertTrue(Schema::hasTable('center_roles'), 'center_roles 表應該存在');
        $this->assertTrue(Schema::hasTable('center_level'), 'center_level 表應該存在');

        // 檢查必要欄位
        $this->assertTrue(Schema::hasColumn('centers', 'name'), 'centers 表應該有 name 欄位');
        $this->assertTrue(Schema::hasColumn('centers', 'center_level_id'), 'centers 表應該有 center_level_id 欄位');
        $this->assertTrue(Schema::hasColumn('center_staff', 'center_id'), 'center_staff 表應該有 center_id 欄位');
        $this->assertTrue(Schema::hasColumn('center_staff', 'account_id'), 'center_staff 表應該有 account_id 欄位');
        $this->assertTrue(Schema::hasColumn('center_staff', 'center_role_id'), 'center_staff 表應該有 center_role_id 欄位');
        $this->assertTrue(Schema::hasColumn('center_staff', 'start_at'), 'center_staff 表應該有 start_at 欄位');

        // 測試資料庫層面的約束
        $center = Center::create([
            'name' => '約束測試中心',
            'center_level_id' => $this->testData->centerLevel->id,
            'status' => 1
        ]);

        // 測試 NOT NULL 約束
        try {
            DB::table('center_staff')->insert([
                'center_id' => $center->id,
                'account_id' => null, // 違反 NOT NULL 約束
                'center_role_id' => $this->testData->roles->get('founder')->id,
                'start_at' => now(),
            ]);
            $this->fail('應該拋出 NOT NULL 約束異常');
        } catch (Exception $e) {
            $this->assertNotEmpty($e->getMessage());
        }

        // 測試有效的插入
        $validId = DB::table('center_staff')->insertGetId([
            'center_id' => $center->id,
            'account_id' => $this->testData->accounts->first()->id,
            'center_role_id' => $this->testData->roles->get('founder')->id,
            'start_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->assertGreaterThan(0, $validId, '有效插入應該返回正數ID');
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        CenterStaff::truncate();
        Center::truncate();
        CenterRole::truncate();
        CenterLevel::truncate();
        Account::truncate();

        parent::tearDown();
    }
}
