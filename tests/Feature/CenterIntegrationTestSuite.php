<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 中心管理整合測試套件
 *
 * 協調和執行所有中心管理相關的整合測試
 * 涵蓋需求: 3.6, 7.3, 7.4
 */
class CenterIntegrationTestSuite extends TestCase
{
    use RefreshDatabase;

    /**
     * 測試套件完整性檢查
     */
    public function test_integration_test_suite_completeness(): void
    {
        // 檢查所有必要的整合測試文件是否存在
        $requiredTestFiles = [
            'tests/Feature/CenterManagementIntegrationTest.php',
            'tests/Feature/CenterConcurrencyIntegrationTest.php',
            'tests/Feature/CenterDataIntegrityTest.php',
            'tests/Feature/CenterErrorHandlingIntegrationTest.php',
        ];

        foreach ($requiredTestFiles as $testFile) {
            $this->assertFileExists(
                base_path($testFile),
                "整合測試文件 {$testFile} 應該存在"
            );
        }
    }

    /**
     * 測試資料庫連線和基礎設定
     */
    public function test_database_connection_and_basic_setup(): void
    {
        // 檢查資料庫連線
        $this->assertTrue(
            DB::connection()->getPdo() !== null,
            '資料庫連線應該正常'
        );

        // 檢查必要的資料表
        $requiredTables = [
            'centers',
            'center_level',
            'center_roles',
            'center_staff',
            'account'
        ];

        foreach ($requiredTables as $table) {
            $this->assertTrue(
                DB::getSchemaBuilder()->hasTable($table),
                "資料表 {$table} 應該存在"
            );
        }
    }

    /**
     * 測試整合測試環境設定
     */
    public function test_integration_test_environment_setup(): void
    {
        // 檢查測試環境配置
        $this->assertEquals('testing', app()->environment(), '應該在測試環境中執行');

        // 檢查日誌配置
        $this->assertNotNull(Log::getLogger(), '日誌系統應該正常配置');

        // 檢查快取配置
        $this->assertTrue(
            config('cache.default') !== null,
            '快取配置應該存在'
        );
    }

    /**
     * 執行完整中心管理流程測試
     */
    public function test_run_complete_center_management_workflow_tests(): void
    {
        $testClass = new CenterManagementIntegrationTest();
        $testClass->setUp();

        try {
            // 執行主要的工作流程測試
            $testClass->test_complete_center_management_workflow();
            $this->assertTrue(true, '完整中心管理流程測試通過');
        } catch (\Exception $e) {
            $this->fail('完整中心管理流程測試失敗: ' . $e->getMessage());
        } finally {
            $testClass->tearDown();
        }
    }

    /**
     * 執行併發操作測試
     */
    public function test_run_concurrency_tests(): void
    {
        $testClass = new CenterConcurrencyIntegrationTest();
        $testClass->setUp();

        $concurrencyTests = [
            'test_singleton_role_race_condition',
            'test_singleton_role_auto_end_concurrency',
            'test_multiple_role_concurrent_assignment',
            'test_mixed_role_concurrent_assignment'
        ];

        $passedTests = 0;
        $failedTests = [];

        foreach ($concurrencyTests as $testMethod) {
            try {
                $testClass->$testMethod();
                $passedTests++;
            } catch (\Exception $e) {
                $failedTests[] = [
                    'method' => $testMethod,
                    'error' => $e->getMessage()
                ];
            }
        }

        $testClass->tearDown();

        // 至少80%的併發測試應該通過
        $successRate = $passedTests / count($concurrencyTests);
        $this->assertGreaterThanOrEqual(
            0.8,
            $successRate,
            '併發測試成功率應該至少80%。失敗的測試: ' . json_encode($failedTests)
        );
    }

    /**
     * 執行資料完整性測試
     */
    public function test_run_data_integrity_tests(): void
    {
        $testClass = new CenterDataIntegrityTest();
        $testClass->setUp();

        $integrityTests = [
            'test_foreign_key_constraints',
            'test_data_type_and_constraint_integrity',
            'test_transaction_integrity',
            'test_relationship_data_integrity'
        ];

        $passedTests = 0;
        $failedTests = [];

        foreach ($integrityTests as $testMethod) {
            try {
                $testClass->$testMethod();
                $passedTests++;
            } catch (\Exception $e) {
                $failedTests[] = [
                    'method' => $testMethod,
                    'error' => $e->getMessage()
                ];
            }
        }

        $testClass->tearDown();

        // 所有資料完整性測試都應該通過
        $this->assertEquals(
            count($integrityTests),
            $passedTests,
            '所有資料完整性測試都應該通過。失敗的測試: ' . json_encode($failedTests)
        );
    }

    /**
     * 執行錯誤處理測試
     */
    public function test_run_error_handling_tests(): void
    {
        $testClass = new CenterErrorHandlingIntegrationTest();
        $testClass->setUp();

        $errorHandlingTests = [
            'test_validation_error_handling_workflow',
            'test_business_logic_error_handling_workflow',
            'test_database_error_handling_workflow',
            'test_error_recovery_and_retry_mechanism'
        ];

        $passedTests = 0;
        $failedTests = [];

        foreach ($errorHandlingTests as $testMethod) {
            try {
                $testClass->$testMethod();
                $passedTests++;
            } catch (\Exception $e) {
                $failedTests[] = [
                    'method' => $testMethod,
                    'error' => $e->getMessage()
                ];
            }
        }

        $testClass->tearDown();

        // 至少90%的錯誤處理測試應該通過
        $successRate = $passedTests / count($errorHandlingTests);
        $this->assertGreaterThanOrEqual(
            0.9,
            $successRate,
            '錯誤處理測試成功率應該至少90%。失敗的測試: ' . json_encode($failedTests)
        );
    }

    /**
     * 測試整合測試覆蓋率
     */
    public function test_integration_test_coverage(): void
    {
        // 檢查需求覆蓋率
        $requiredCoverage = [
            '3.6' => '併發角色指派和單例角色唯一性',
            '7.3' => '資料完整性檢查',
            '7.4' => '錯誤處理流程'
        ];

        $coveredRequirements = [];

        // 檢查 CenterManagementIntegrationTest 覆蓋的需求
        $managementTestContent = file_get_contents(base_path('tests/Feature/CenterManagementIntegrationTest.php'));
        if (strpos($managementTestContent, '需求: 3.6') !== false) {
            $coveredRequirements[] = '3.6';
        }
        if (strpos($managementTestContent, '需求: 7.3') !== false) {
            $coveredRequirements[] = '7.3';
        }
        if (strpos($managementTestContent, '需求: 7.4') !== false) {
            $coveredRequirements[] = '7.4';
        }

        // 檢查 CenterConcurrencyIntegrationTest 覆蓋的需求
        $concurrencyTestContent = file_get_contents(base_path('tests/Feature/CenterConcurrencyIntegrationTest.php'));
        if (strpos($concurrencyTestContent, '需求: 3.6') !== false && !in_array('3.6', $coveredRequirements)) {
            $coveredRequirements[] = '3.6';
        }

        // 檢查 CenterDataIntegrityTest 覆蓋的需求
        $integrityTestContent = file_get_contents(base_path('tests/Feature/CenterDataIntegrityTest.php'));
        if (strpos($integrityTestContent, '需求: 7.3') !== false && !in_array('7.3', $coveredRequirements)) {
            $coveredRequirements[] = '7.3';
        }

        // 檢查 CenterErrorHandlingIntegrationTest 覆蓋的需求
        $errorTestContent = file_get_contents(base_path('tests/Feature/CenterErrorHandlingIntegrationTest.php'));
        if (strpos($errorTestContent, '需求: 7.4') !== false && !in_array('7.4', $coveredRequirements)) {
            $coveredRequirements[] = '7.4';
        }

        // 驗證所有必要需求都被覆蓋
        foreach (array_keys($requiredCoverage) as $requirement) {
            $this->assertContains(
                $requirement,
                $coveredRequirements,
                "需求 {$requirement} ({$requiredCoverage[$requirement]}) 應該被整合測試覆蓋"
            );
        }
    }

    /**
     * 測試整合測試執行效能
     */
    public function test_integration_test_performance(): void
    {
        $startTime = microtime(true);

        // 執行一個簡化的整合測試流程
        $testClass = new CenterManagementIntegrationTest();
        $testClass->setUp();

        try {
            $testClass->test_complete_center_management_workflow();
        } catch (\Exception $e) {
            // 忽略測試結果，只關注效能
        } finally {
            $testClass->tearDown();
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 整合測試應該在合理時間內完成（例如30秒）
        $this->assertLessThan(
            30,
            $executionTime,
            "整合測試執行時間應該少於30秒，實際執行時間: {$executionTime}秒"
        );

        Log::info('整合測試效能統計', [
            'execution_time' => $executionTime,
            'memory_usage' => memory_get_peak_usage(true),
            'timestamp' => now()
        ]);
    }

    /**
     * 生成整合測試報告
     */
    public function test_generate_integration_test_report(): void
    {
        $report = [
            'test_suite' => 'Center Management Integration Tests',
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'database_connection' => config('database.default'),
            'test_files' => [
                'CenterManagementIntegrationTest.php' => '完整中心管理流程測試',
                'CenterConcurrencyIntegrationTest.php' => '併發角色指派測試',
                'CenterDataIntegrityTest.php' => '資料完整性檢查測試',
                'CenterErrorHandlingIntegrationTest.php' => '錯誤處理流程測試'
            ],
            'requirements_coverage' => [
                '3.6' => '併發角色指派和單例角色唯一性',
                '7.3' => '資料完整性檢查',
                '7.4' => '錯誤處理流程'
            ],
            'test_categories' => [
                'workflow_tests' => '完整業務流程測試',
                'concurrency_tests' => '併發操作安全性測試',
                'integrity_tests' => '資料完整性和約束測試',
                'error_handling_tests' => '錯誤處理和恢復測試'
            ]
        ];

        // 記錄測試報告
        Log::info('中心管理整合測試報告', $report);

        // 驗證報告完整性
        $this->assertArrayHasKey('test_suite', $report);
        $this->assertArrayHasKey('requirements_coverage', $report);
        $this->assertCount(3, $report['requirements_coverage']);
        $this->assertCount(4, $report['test_files']);

        $this->assertTrue(true, '整合測試報告生成成功');
    }
}
