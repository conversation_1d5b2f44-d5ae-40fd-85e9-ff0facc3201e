<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use App\Services\Center\CenterService;
use App\Services\Center\CenterStaffService;
use Exception;

/**
 * 中心管理功能整合測試
 *
 * 涵蓋需求: 3.6, 7.3, 7.4
 * 測試完整的中心管理流程、併發角色指派、資料完整性和錯誤處理
 */
class CenterManagementIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $centerService;
    protected $centerStaffService;
    protected $testCenterLevel;
    protected $testAccounts;
    protected $testRoles;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerService = app(CenterService::class);
        $this->centerStaffService = app(CenterStaffService::class);

        $this->setupTestData();
    }

    /**
     * 設置測試資料
     */
    private function setupTestData(): void
    {
        // 建立測試用中心等級
        $this->testCenterLevel = CenterLevel::create([
            'name' => '測試等級',
            'level' => 1,
            'status' => 1
        ]);

        // 建立測試用帳號
        $this->testAccounts = collect();
        for ($i = 1; $i <= 10; $i++) {
            $account = Account::create([
                'number' => 'TEST' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'name' => '測試會員' . $i,
                'email' => 'test' . $i . '@example.com',
                'status' => 1
            ]);
            $this->testAccounts->push($account);
        }

        // 建立測試用角色
        $roles = [
            ['code' => 'founder', 'name' => '中心發起人', 'is_singleton' => true],
            ['code' => 'director', 'name' => '中心總監', 'is_singleton' => true],
            ['code' => 'executive_director', 'name' => '大總監', 'is_singleton' => true],
            ['code' => 'lecturer', 'name' => '講師', 'is_singleton' => false],
            ['code' => 'market', 'name' => '行政/廣告人員', 'is_singleton' => false],
            ['code' => 'sales', 'name' => '業務', 'is_singleton' => false],
        ];

        $this->testRoles = collect();
        foreach ($roles as $roleData) {
            $role = CenterRole::create($roleData);
            $this->testRoles->put($roleData['code'], $role);
        }
    }

    /**
     * 測試完整中心管理流程
     * 需求: 1.1, 1.3, 1.5, 3.4, 4.3
     */
    public function test_complete_center_management_workflow(): void
    {
        // 步驟 1: 建立中心
        $centerData = [
            'name' => '測試中心A',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ];

        $center = $this->centerService->createCenter($centerData);

        $this->assertInstanceOf(Center::class, $center);
        $this->assertEquals('測試中心A', $center->name);
        $this->assertEquals($this->testCenterLevel->id, $center->center_level_id);

        // 步驟 2: 指派單例角色 (中心發起人)
        $founderAccount = $this->testAccounts->first();
        $founderRole = $this->testRoles->get('founder');

        $this->centerStaffService->assignRole(
            $center->id,
            $founderAccount->id,
            $founderRole->id,
            true // auto_end_existing
        );

        // 驗證角色指派成功
        $activeFounder = $this->centerStaffService->getActiveStaff($center->id, $founderRole->id);
        $this->assertCount(1, $activeFounder);
        $this->assertEquals($founderAccount->id, $activeFounder->first()->account_id);

        // 步驟 3: 指派多人角色 (講師)
        $lecturerRole = $this->testRoles->get('lecturer');
        $lecturerAccounts = $this->testAccounts->slice(1, 3); // 取3個講師

        foreach ($lecturerAccounts as $account) {
            $this->centerStaffService->assignRole(
                $center->id,
                $account->id,
                $lecturerRole->id
            );
        }

        // 驗證多人角色指派
        $activeLecturers = $this->centerStaffService->getActiveStaff($center->id, $lecturerRole->id);
        $this->assertCount(3, $activeLecturers);

        // 步驟 4: 更新中心資訊
        $updateData = [
            'name' => '更新後的測試中心A',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ];

        $updatedCenter = $this->centerService->updateCenter($center->id, $updateData);
        $this->assertEquals('更新後的測試中心A', $updatedCenter->name);

        // 步驟 5: 嘗試刪除有現役人員的中心 (應該失敗)
        $canDelete = $this->centerService->canDeleteCenter($center->id);
        $this->assertFalse($canDelete);

        // 步驟 6: 結束所有角色後刪除中心
        $allActiveStaff = CenterStaff::where('center_id', $center->id)
            ->whereNull('end_at')
            ->get();

        foreach ($allActiveStaff as $staff) {
            $this->centerStaffService->endActiveRole($staff->id);
        }

        // 現在應該可以刪除
        $canDeleteNow = $this->centerService->canDeleteCenter($center->id);
        $this->assertTrue($canDeleteNow);

        $this->centerService->deleteCenter($center->id);

        // 驗證中心已被刪除
        $this->assertNull(Center::find($center->id));
    }

    /**
     * 測試併發角色指派
     * 需求: 3.6
     */
    public function test_concurrent_role_assignment(): void
    {
        $center = $this->centerService->createCenter([
            'name' => '併發測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        $directorRole = $this->testRoles->get('director');
        $account1 = $this->testAccounts->get(0);
        $account2 = $this->testAccounts->get(1);

        // 模擬併發情況：兩個請求同時嘗試指派同一個單例角色
        DB::beginTransaction();

        try {
            // 第一個指派
            $this->centerStaffService->assignRole(
                $center->id,
                $account1->id,
                $directorRole->id,
                true
            );

            // 檢查是否有現役的單例角色
            $hasActiveSingleton = $this->centerStaffService->hasActiveSingleton(
                $center->id,
                $directorRole->id
            );
            $this->assertTrue($hasActiveSingleton);

            // 第二個指派 (應該自動結束第一個)
            $this->centerStaffService->assignRole(
                $center->id,
                $account2->id,
                $directorRole->id,
                true
            );

            DB::commit();

            // 驗證只有一個現役總監
            $activeDirectors = $this->centerStaffService->getActiveStaff($center->id, $directorRole->id);
            $this->assertCount(1, $activeDirectors);
            $this->assertEquals($account2->id, $activeDirectors->first()->account_id);

            // 驗證第一個指派已被結束
            $endedStaff = CenterStaff::where('center_id', $center->id)
                ->where('account_id', $account1->id)
                ->where('center_role_id', $directorRole->id)
                ->whereNotNull('end_at')
                ->first();

            $this->assertNotNull($endedStaff);
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * 測試併發多人角色指派
     * 需求: 4.3, 4.4
     */
    public function test_concurrent_multiple_role_assignment(): void
    {
        $center = $this->centerService->createCenter([
            'name' => '多人角色併發測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        $lecturerRole = $this->testRoles->get('lecturer');
        $accounts = $this->testAccounts->slice(0, 5);

        // 模擬多個併發請求同時指派講師角色
        $promises = [];

        foreach ($accounts as $account) {
            DB::beginTransaction();
            try {
                $this->centerStaffService->assignRole(
                    $center->id,
                    $account->id,
                    $lecturerRole->id
                );
                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
                // 記錄但不中斷測試
                Log::warning('Concurrent assignment failed', [
                    'account_id' => $account->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 驗證所有講師都被成功指派
        $activeLecturers = $this->centerStaffService->getActiveStaff($center->id, $lecturerRole->id);
        $this->assertGreaterThanOrEqual(4, $activeLecturers->count()); // 至少4個成功
        $this->assertLessThanOrEqual(5, $activeLecturers->count()); // 最多5個
    }

    /**
     * 測試資料完整性檢查
     * 需求: 7.3
     */
    public function test_data_integrity_checks(): void
    {
        $center = $this->centerService->createCenter([
            'name' => '資料完整性測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        // 測試 1: 檢查外鍵約束
        $this->expectException(Exception::class);

        try {
            // 嘗試指派不存在的帳號
            $this->centerStaffService->assignRole(
                $center->id,
                99999, // 不存在的帳號ID
                $this->testRoles->get('founder')->id
            );
        } catch (Exception $e) {
            // 預期的異常
            $this->assertStringContainsString('account', strtolower($e->getMessage()));
        }

        // 測試 2: 檢查角色資料完整性
        $founderRole = $this->testRoles->get('founder');
        $account = $this->testAccounts->first();

        // 正常指派
        $this->centerStaffService->assignRole(
            $center->id,
            $account->id,
            $founderRole->id
        );

        // 檢查資料庫記錄完整性
        $staffRecord = CenterStaff::where('center_id', $center->id)
            ->where('account_id', $account->id)
            ->where('center_role_id', $founderRole->id)
            ->first();

        $this->assertNotNull($staffRecord);
        $this->assertNotNull($staffRecord->start_at);
        $this->assertNull($staffRecord->end_at);
        $this->assertEquals($center->id, $staffRecord->center_id);
        $this->assertEquals($account->id, $staffRecord->account_id);
        $this->assertEquals($founderRole->id, $staffRecord->center_role_id);

        // 測試 3: 檢查軟刪除完整性
        $this->centerStaffService->endActiveRole($staffRecord->id);

        $staffRecord->refresh();
        $this->assertNotNull($staffRecord->end_at);

        // 測試 4: 檢查關聯資料完整性
        $centerWithRelations = Center::with(['centerLevel', 'activeStaff', 'allStaff'])
            ->find($center->id);

        $this->assertNotNull($centerWithRelations->centerLevel);
        $this->assertEquals($this->testCenterLevel->id, $centerWithRelations->centerLevel->id);
    }

    /**
     * 測試錯誤處理流程
     * 需求: 7.4
     */
    public function test_error_handling_workflows(): void
    {
        // 測試 1: 無效資料處理
        try {
            $this->centerService->createCenter([
                'name' => '', // 空名稱
                'center_level_id' => 99999, // 不存在的等級
                'status' => 1
            ]);
            $this->fail('應該拋出驗證異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('validation', strtolower($e->getMessage()));
        }

        // 測試 2: 角色指派錯誤處理
        $center = $this->centerService->createCenter([
            'name' => '錯誤處理測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        // 嘗試指派不存在的角色
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $this->testAccounts->first()->id,
                99999 // 不存在的角色ID
            );
            $this->fail('應該拋出角色不存在異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('role', strtolower($e->getMessage()));
        }

        // 測試 3: 單例角色衝突錯誤處理
        $founderRole = $this->testRoles->get('founder');
        $account1 = $this->testAccounts->get(0);
        $account2 = $this->testAccounts->get(1);

        // 先指派第一個發起人
        $this->centerStaffService->assignRole(
            $center->id,
            $account1->id,
            $founderRole->id
        );

        // 嘗試指派第二個發起人但不自動結束現有的
        try {
            $this->centerStaffService->assignRole(
                $center->id,
                $account2->id,
                $founderRole->id,
                false // 不自動結束現有角色
            );
            $this->fail('應該拋出單例角色衝突異常');
        } catch (Exception $e) {
            $this->assertStringContainsString('singleton', strtolower($e->getMessage()));
        }

        // 測試 4: 刪除錯誤處理
        try {
            $this->centerService->deleteCenter($center->id);
            $this->fail('應該拋出無法刪除異常（有現役人員）');
        } catch (Exception $e) {
            $this->assertStringContainsString('active', strtolower($e->getMessage()));
        }

        // 測試 5: 資料庫交易錯誤處理
        DB::beginTransaction();

        try {
            // 模擬資料庫錯誤
            DB::statement('DROP TABLE IF EXISTS temp_test_table');
            DB::statement('CREATE TABLE temp_test_table (id INT)');

            // 嘗試在交易中執行會失敗的操作
            DB::statement('INSERT INTO temp_test_table VALUES ("invalid_int")');

            DB::commit();
            $this->fail('應該拋出資料庫錯誤');
        } catch (Exception $e) {
            DB::rollback();
            $this->assertNotEmpty($e->getMessage());
        }
    }

    /**
     * 測試複雜業務流程的錯誤恢復
     * 需求: 7.4
     */
    public function test_complex_workflow_error_recovery(): void
    {
        $center = $this->centerService->createCenter([
            'name' => '複雜流程測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        // 模擬複雜的角色指派流程中發生錯誤
        DB::beginTransaction();

        try {
            // 步驟 1: 指派發起人
            $founderRole = $this->testRoles->get('founder');
            $this->centerStaffService->assignRole(
                $center->id,
                $this->testAccounts->get(0)->id,
                $founderRole->id
            );

            // 步驟 2: 指派總監
            $directorRole = $this->testRoles->get('director');
            $this->centerStaffService->assignRole(
                $center->id,
                $this->testAccounts->get(1)->id,
                $directorRole->id
            );

            // 步驟 3: 模擬錯誤 - 嘗試指派不存在的帳號
            $this->centerStaffService->assignRole(
                $center->id,
                99999, // 不存在的帳號
                $this->testRoles->get('lecturer')->id
            );

            DB::commit();
            $this->fail('應該拋出異常');
        } catch (Exception $e) {
            DB::rollback();

            // 驗證交易回滾後沒有任何角色被指派
            $allStaff = CenterStaff::where('center_id', $center->id)->count();
            $this->assertEquals(0, $allStaff);
        }

        // 測試錯誤恢復 - 重新執行正確的流程
        $founderRole = $this->testRoles->get('founder');
        $directorRole = $this->testRoles->get('director');
        $lecturerRole = $this->testRoles->get('lecturer');

        $this->centerStaffService->assignRole(
            $center->id,
            $this->testAccounts->get(0)->id,
            $founderRole->id
        );

        $this->centerStaffService->assignRole(
            $center->id,
            $this->testAccounts->get(1)->id,
            $directorRole->id
        );

        $this->centerStaffService->assignRole(
            $center->id,
            $this->testAccounts->get(2)->id,
            $lecturerRole->id
        );

        // 驗證恢復後的狀態
        $activeStaff = CenterStaff::where('center_id', $center->id)
            ->whereNull('end_at')
            ->count();
        $this->assertEquals(3, $activeStaff);
    }

    /**
     * 測試系統負載下的資料完整性
     * 需求: 3.6, 7.3
     */
    public function test_data_integrity_under_load(): void
    {
        $center = $this->centerService->createCenter([
            'name' => '負載測試中心',
            'center_level_id' => $this->testCenterLevel->id,
            'status' => 1
        ]);

        $lecturerRole = $this->testRoles->get('lecturer');
        $successCount = 0;
        $errorCount = 0;

        // 模擬高負載情況下的多次角色指派
        for ($i = 0; $i < 20; $i++) {
            try {
                $account = $this->testAccounts->get($i % $this->testAccounts->count());

                $this->centerStaffService->assignRole(
                    $center->id,
                    $account->id,
                    $lecturerRole->id
                );

                $successCount++;
            } catch (Exception $e) {
                $errorCount++;
                Log::info('Load test assignment failed', [
                    'iteration' => $i,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 驗證資料完整性
        $actualStaffCount = CenterStaff::where('center_id', $center->id)
            ->where('center_role_id', $lecturerRole->id)
            ->whereNull('end_at')
            ->count();

        // 由於可能有重複指派同一個帳號，實際數量可能小於成功次數
        $this->assertLessThanOrEqual($successCount, $actualStaffCount);
        $this->assertGreaterThan(0, $actualStaffCount);

        // 驗證沒有資料損壞
        $allStaffRecords = CenterStaff::where('center_id', $center->id)->get();

        foreach ($allStaffRecords as $staff) {
            $this->assertNotNull($staff->center_id);
            $this->assertNotNull($staff->account_id);
            $this->assertNotNull($staff->center_role_id);
            $this->assertNotNull($staff->start_at);

            // 驗證外鍵關聯存在
            $this->assertNotNull($staff->center);
            $this->assertNotNull($staff->account);
            $this->assertNotNull($staff->centerRole);
        }
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        CenterStaff::truncate();
        Center::truncate();
        CenterRole::truncate();
        CenterLevel::truncate();
        Account::truncate();

        parent::tearDown();
    }
}
