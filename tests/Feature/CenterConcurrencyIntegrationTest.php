<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illt\Facades\Log;
use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use App\Models\Main\Account;
use App\Services\Center\CenterStaffService;
use Exception;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Event;

/**
 * 中心管理併發操作整合測試
 *
 * 專門測試併發情況下的角色指派、資料完整性和競爭條件
 * 涵蓋需求: 3.6
 */
class CenterConcurrencyIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $centerStaffService;
    protected $testCenter;
    protected $testAccounts;
    protected $testRoles;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerStaffService = app(CenterStaffService::class);
        $this->setupTestData();
    }

    private function setupTestData(): void
    {
        // 建立測試中心等級
        $centerLevel = CenterLevel::create([
            'name' => '併發測試等級',
            'level' => 1,
            'status' => 1
        ]);

        // 建立測試中心
        $this->testCenter = Center::create([
            'name' => '併發測試中心',
            'center_level_id' => $centerLevel->id,
            'status' => 1
        ]);

        // 建立測試帳號
        $this->testAccounts = collect();
        for ($i = 1; $i <= 20; $i++) {
            $account = Account::create([
                'number' => 'CONC' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'name' => '併發測試會員' . $i,
                'email' => 'conc' . $i . '@example.com',
                'status' => 1
            ]);
            $this->testAccounts->push($account);
        }

        // 建立測試角色
        $roles = [
            ['code' => 'founder', 'name' => '中心發起人', 'is_singleton' => true],
            ['code' => 'director', 'name' => '中心總監', 'is_singleton' => true],
            ['code' => 'executive_director', 'name' => '大總監', 'is_singleton' => true],
            ['code' => 'lecturer', 'name' => '講師', 'is_singleton' => false],
            ['code' => 'market', 'name' => '行政/廣告人員', 'is_singleton' => false],
        ];

        $this->testRoles = collect();
        foreach ($roles as $roleData) {
            $role = CenterRole::create($roleData);
            $this->testRoles->put($roleData['code'], $role);
        }
    }

    /**
     * 測試單例角色的併發指派競爭條件
     * 需求: 3.6
     */
    public function test_singleton_role_race_condition(): void
    {
        $founderRole = $this->testRoles->get('founder');
        $account1 = $this->testAccounts->get(0);
        $account2 = $this->testAccounts->get(1);

        $results = [];
        $exceptions = [];

        // 模擬兩個併發請求同時嘗試指派發起人角色
        $processes = [];

        // 第一個程序
        $processes[] = function() use ($founderRole, $account1, &$results, &$exceptions) {
            try {
                DB::beginTransaction();

                // 模擬網路延遲
                usleep(rand(1000, 5000));

                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $account1->id,
                    $founderRole->id,
                    false // 不自動結束現有角色，測試衝突處理
                );

                DB::commit();
                $results[] = 'account1_success';

            } catch (Exception $e) {
                DB::rollback();
                $exceptions[] = [
                    'account' => 'account1',
                    'message' => $e->getMessage()
                ];
            }
        };

        // 第二個程序
        $processes[] = function() use ($founderRole, $account2, &$results, &$exceptions) {
            try {
                DB::beginTransaction();

                // 模擬網路延遲
                usleep(rand(1000, 5000));

                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $account2->id,
                    $founderRole->id,
                    false // 不自動結束現有角色，測試衝突處理
                );

                DB::commit();
                $results[] = 'account2_success';

            } catch (Exception $e) {
                DB::rollback();
                $exceptions[] = [
                    'account' => 'account2',
                    'message' => $e->getMessage()
                ];
            }
        };

        // 執行併發程序
        foreach ($processes as $process) {
            $process();
        }

        // 驗證結果：應該只有一個成功，一個失敗
        $this->assertEquals(1, count($results), '應該只有一個角色指派成功');
        $this->assertEquals(1, count($exceptions), '應該有一個角色指派失敗');

        // 驗證資料庫狀態：只有一個現役發起人
        $activeFounders = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $founderRole->id)
            ->whereNull('end_at')
            ->count();

        $this->assertEquals(1, $activeFounders, '應該只有一個現役發起人');
    }

    /**
     * 測試單例角色自動結束的併發安全性
     * 需求: 3.6
     */
    public function test_singleton_role_auto_end_concurrency(): void
    {
        $directorRole = $this->testRoles->get('director');

        // 先指派一個總監
        $existingDirector = $this->testAccounts->get(0);
        $this->centerStaffService->assignRole(
            $this->testCenter->id,
            $existingDirector->id,
            $directorRole->id
        );

        // 驗證現有總監已指派
        $this->assertTrue(
            $this->centerStaffService->hasActiveSingleton(
                $this->testCenter->id,
                $directorRole->id
            )
        );

        $newDirector1 = $this->testAccounts->get(1);
        $newDirector2 = $this->testAccounts->get(2);

        $results = [];
        $exceptions = [];

        // 模擬兩個併發請求同時嘗試替換總監
        $processes = [
            function() use ($directorRole, $newDirector1, &$results, &$exceptions) {
                try {
                    $this->centerStaffService->assignRole(
                        $this->testCenter->id,
                        $newDirector1->id,
                        $directorRole->id,
                        true // 自動結束現有角色
                    );
                    $results[] = 'director1_assigned';
                } catch (Exception $e) {
                    $exceptions[] = [
                        'director' => 'director1',
                        'message' => $e->getMessage()
                    ];
                }
            },

            function() use ($directorRole, $newDirector2, &$results, &$exceptions) {
                try {
                    // 稍微延遲以模擬真實併發情況
                    usleep(1000);

                    $this->centerStaffService->assignRole(
                        $this->testCenter->id,
                        $newDirector2->id,
                        $directorRole->id,
                        true // 自動結束現有角色
                    );
                    $results[] = 'director2_assigned';
                } catch (Exception $e) {
                    $exceptions[] = [
                        'director' => 'director2',
                        'message' => $e->getMessage()
                    ];
                }
            }
        ];

        // 執行併發程序
        foreach ($processes as $process) {
            $process();
        }

        // 驗證最終狀態：應該只有一個現役總監
        $activeDirectors = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $directorRole->id)
            ->whereNull('end_at')
            ->get();

        $this->assertEquals(1, $activeDirectors->count(), '應該只有一個現役總監');

        // 驗證舊總監已被結束
        $endedDirectors = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $directorRole->id)
            ->whereNotNull('end_at')
            ->count();

        $this->assertGreaterThanOrEqual(1, $endedDirectors, '至少有一個總監被結束');

        // 驗證總的記錄數正確
        $totalDirectorRecords = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $directorRole->id)
            ->count();

        $this->assertGreaterThanOrEqual(2, $totalDirectorRecords, '應該有至少2個總監記錄');
    }

    /**
     * 測試多人角色的併發指派
     * 需求: 4.3, 4.4
     */
    public function test_multiple_role_concurrent_assignment(): void
    {
        $lecturerRole = $this->testRoles->get('lecturer');
        $accounts = $this->testAccounts->slice(0, 10);

        $successCount = 0;
        $errorCount = 0;
        $results = [];

        // 模擬多個併發請求同時指派講師角色
        foreach ($accounts as $index => $account) {
            try {
                // 模擬不同的網路延遲
                usleep(rand(100, 1000));

                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $account->id,
                    $lecturerRole->id
                );

                $successCount++;
                $results[] = "lecturer_{$index}_success";

            } catch (Exception $e) {
                $errorCount++;
                Log::warning('Concurrent lecturer assignment failed', [
                    'account_id' => $account->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 驗證結果
        $this->assertGreaterThan(0, $successCount, '至少應該有一些講師指派成功');

        // 驗證資料庫中的講師數量
        $activeLecturers = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $lecturerRole->id)
            ->whereNull('end_at')
            ->count();

        $this->assertEquals($successCount, $activeLecturers, '資料庫中的講師數量應該等於成功指派的數量');

        // 驗證沒有重複指派
        $uniqueLecturers = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $lecturerRole->id)
            ->whereNull('end_at')
            ->distinct('account_id')
            ->count();

        $this->assertEquals($activeLecturers, $uniqueLecturers, '不應該有重複的講師指派');
    }

    /**
     * 測試混合角色併發指派
     * 需求: 3.6, 4.3
     */
    public function test_mixed_role_concurrent_assignment(): void
    {
        $founderRole = $this->testRoles->get('founder');
        $directorRole = $this->testRoles->get('director');
        $lecturerRole = $this->testRoles->get('lecturer');
        $marketRole = $this->testRoles->get('market');

        $operations = [
            // 單例角色操作
            ['type' => 'singleton', 'role' => $founderRole, 'account' => $this->testAccounts->get(0)],
            ['type' => 'singleton', 'role' => $directorRole, 'account' => $this->testAccounts->get(1)],

            // 多人角色操作
            ['type' => 'multiple', 'role' => $lecturerRole, 'account' => $this->testAccounts->get(2)],
            ['type' => 'multiple', 'role' => $lecturerRole, 'account' => $this->testAccounts->get(3)],
            ['type' => 'multiple', 'role' => $marketRole, 'account' => $this->testAccounts->get(4)],
            ['type' => 'multiple', 'role' => $marketRole, 'account' => $this->testAccounts->get(5)],
        ];

        $results = [];
        $exceptions = [];

        // 併發執行所有操作
        foreach ($operations as $index => $operation) {
            try {
                // 模擬隨機延遲
                usleep(rand(100, 2000));

                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $operation['account']->id,
                    $operation['role']->id,
                    true // 自動結束現有單例角色
                );

                $results[] = [
                    'index' => $index,
                    'type' => $operation['type'],
                    'role' => $operation['role']->code,
                    'account' => $operation['account']->id,
                    'status' => 'success'
                ];

            } catch (Exception $e) {
                $exceptions[] = [
                    'index' => $index,
                    'type' => $operation['type'],
                    'role' => $operation['role']->code,
                    'account' => $operation['account']->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        // 驗證單例角色結果
        $activeFounders = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $founderRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertLessThanOrEqual(1, $activeFounders, '發起人應該最多只有一個');

        $activeDirectors = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $directorRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertLessThanOrEqual(1, $activeDirectors, '總監應該最多只有一個');

        // 驗證多人角色結果
        $activeLecturers = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $lecturerRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertGreaterThanOrEqual(0, $activeLecturers, '講師可以有多個');

        $activeMarketers = CenterStaff::where('center_id', $this->testCenter->id)
            ->where('center_role_id', $marketRole->id)
            ->whereNull('end_at')
            ->count();
        $this->assertGreaterThanOrEqual(0, $activeMarketers, '行政人員可以有多個');

        // 記錄測試結果以供分析
        Log::info('Mixed role concurrent assignment test results', [
            'successful_operations' => count($results),
            'failed_operations' => count($exceptions),
            'active_founders' => $activeFounders,
            'active_directors' => $activeDirectors,
            'active_lecturers' => $activeLecturers,
            'active_marketers' => $activeMarketers,
        ]);
    }

    /**
     * 測試資料庫鎖定和死鎖處理
     * 需求: 3.6
     */
    public function test_database_locking_and_deadlock_handling(): void
    {
        $founderRole = $this->testRoles->get('founder');
        $directorRole = $this->testRoles->get('director');

        $deadlockCount = 0;
        $successCount = 0;

        // 模擬可能導致死鎖的操作序列
        for ($i = 0; $i < 5; $i++) {
            try {
                DB::beginTransaction();

                // 操作 1: 指派發起人
                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $this->testAccounts->get($i % 4)->id,
                    $founderRole->id,
                    true
                );

                // 模擬處理時間
                usleep(rand(1000, 3000));

                // 操作 2: 指派總監
                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $this->testAccounts->get(($i + 1) % 4)->id,
                    $directorRole->id,
                    true
                );

                DB::commit();
                $successCount++;

            } catch (Exception $e) {
                DB::rollback();

                if (strpos(strtolower($e->getMessage()), 'deadlock') !== false) {
                    $deadlockCount++;
                    Log::warning('Deadlock detected and handled', [
                        'iteration' => $i,
                        'error' => $e->getMessage()
                    ]);
                } else {
                    // 重新拋出非死鎖異常
                    throw $e;
                }
            }
        }

        // 驗證系統能夠處理死鎖情況
        $this->assertGreaterThan(0, $successCount, '至少應該有一些操作成功');

        // 驗證最終資料狀態一致
        $totalActiveStaff = CenterStaff::where('center_id', $this->testCenter->id)
            ->whereNull('end_at')
            ->count();
        $this->assertGreaterThan(0, $totalActiveStaff, '應該有現役人員');

        Log::info('Deadlock handling test results', [
            'success_count' => $successCount,
            'deadlock_count' => $deadlockCount,
            'total_active_staff' => $totalActiveStaff
        ]);
    }

    /**
     * 測試高併發下的系統穩定性
     * 需求: 3.6, 7.3
     */
    public function test_high_concurrency_system_stability(): void
    {
        $lecturerRole = $this->testRoles->get('lecturer');
        $marketRole = $this->testRoles->get('market');

        $totalOperations = 50;
        $successCount = 0;
        $errorCount = 0;
        $startTime = microtime(true);

        // 高併發操作
        for ($i = 0; $i < $totalOperations; $i++) {
            try {
                $account = $this->testAccounts->get($i % $this->testAccounts->count());
                $role = ($i % 2 === 0) ? $lecturerRole : $marketRole;

                $this->centerStaffService->assignRole(
                    $this->testCenter->id,
                    $account->id,
                    $role->id
                );

                $successCount++;

                // 模擬真實的處理間隔
                if ($i % 10 === 0) {
                    usleep(100);
                }

            } catch (Exception $e) {
                $errorCount++;
                Log::debug('High concurrency operation failed', [
                    'iteration' => $i,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 驗證系統穩定性指標
        $successRate = $successCount / $totalOperations;
        $this->assertGreaterThan(0.8, $successRate, '成功率應該超過80%');

        // 驗證資料完整性
        $totalActiveStaff = CenterStaff::where('center_id', $this->testCenter->id)
            ->whereNull('end_at')
            ->count();
        $this->assertGreaterThan(0, $totalActiveStaff, '應該有現役人員');

        // 驗證沒有資料損壞
        $corruptedRecords = CenterStaff::where('center_id', $this->testCenter->id)
            ->where(function($query) {
                $query->whereNull('account_id')
                      ->orWhereNull('center_role_id')
                      ->orWhereNull('start_at');
            })
            ->count();
        $this->assertEquals(0, $corruptedRecords, '不應該有損壞的記錄');

        Log::info('High concurrency stability test results', [
            'total_operations' => $totalOperations,
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'success_rate' => $successRate,
            'execution_time' => $executionTime,
            'operations_per_second' => $totalOperations / $executionTime,
            'total_active_staff' => $totalActiveStaff
        ]);
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        CenterStaff::where('center_id', $this->testCenter->id)->delete();

        parent::tearDown();
    }
}
uminate
