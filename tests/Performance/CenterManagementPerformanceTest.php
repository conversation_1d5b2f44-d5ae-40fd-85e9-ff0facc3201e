<?php

namespace Tests\Performance;

use Tests\TestCase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Services\Center\CenterService;
use App\Services\Center\CenterCacheService;

/**
 * 中心管理效能測試
 *
 * 驗證效能優化的實際效果
 */
class CenterManagementPerformanceTest extends TestCase
{
    protected $centerService;
    protected $cacheService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->centerService = new CenterService();
        $this->cacheService = new CenterCacheService();
    }

    /**
     * 測試快取服務效能
     */
    public function test_cache_service_performance()
    {
        // 清除快取
        Cache::flush();

        // 測試第一次查詢（無快取）
        $startTime = microtime(true);
        $levels1 = $this->cacheService->getCenterLevels();
        $firstQueryTime = microtime(true) - $startTime;

        // 測試第二次查詢（有快取）
        $startTime = microtime(true);
        $levels2 = $this->cacheService->getCenterLevels();
        $cachedQueryTime = microtime(true) - $startTime;

        // 驗證快取查詢更快
        $this->assertLessThan($firstQueryTime, $cachedQueryTime);
        $this->assertEquals($levels1->toArray(), $levels2->toArray());

        echo "\n第一次查詢時間: " . number_format($firstQueryTime * 1000, 2) . "ms";
        echo "\n快取查詢時間: " . number_format($cachedQueryTime * 1000, 2) . "ms";
        echo "\n效能提升: " . number_format(($firstQueryTime - $cachedQueryTime) / $firstQueryTime * 100, 1) . "%\n";
    }

    /**
     * 測試會員搜尋效能
     */
    public function test_account_search_performance()
    {
        $searchQueries = ['A001', 'A002', 'A003', 'A004', 'A005'];
        $totalTime = 0;
        $queryCount = 0;

        foreach ($searchQueries as $query) {
            $startTime = microtime(true);
            $results = $this->centerService->searchAccounts($query, 10);
            $queryTime = microtime(true) - $startTime;

            $totalTime += $queryTime;
            $queryCount++;

            echo "\n搜尋 '{$query}': " . number_format($queryTime * 1000, 2) . "ms, 結果數: " . $results->count();
        }

        $averageTime = $totalTime / $queryCount;
        echo "\n平均搜尋時間: " . number_format($averageTime * 1000, 2) . "ms";

        // 驗證平均搜尋時間在合理範圍內
        $this->assertLessThan(0.1, $averageTime, '會員搜尋效能不佳');
    }

    /**
     * 測試中心列表查詢效能
     */
    public function test_center_list_performance()
    {
        $filters = [
            [],
            ['name' => '測試'],
            ['center_level_id' => 1],
            ['status' => 1],
            ['name' => '測試', 'center_level_id' => 1, 'status' => 1]
        ];

        foreach ($filters as $index => $filter) {
            $startTime = microtime(true);
            $centers = $this->centerService->getAllCenters($filter, 15);
            $queryTime = microtime(true) - $startTime;

            $filterDesc = empty($filter) ? '無篩選' : implode(', ', array_keys($filter));
            echo "\n查詢條件 '{$filterDesc}': " . number_format($queryTime * 1000, 2) . "ms, 結果數: " . $centers->count();

            // 驗證查詢時間在合理範圍內
            $this->assertLessThan(0.2, $queryTime, "中心列表查詢效能不佳: {$filterDesc}");
        }
    }

    /**
     * 測試併發操作效能
     */
    public function test_concurrent_operations_performance()
    {
        $operations = 50;
        $startTime = microtime(true);

        for ($i = 0; $i < $operations; $i++) {
            // 模擬併發操作
            $this->cacheService->getCenterLevels();
            $this->cacheService->getCenterRoles();
            $this->centerService->searchAccounts('A00', 5);
        }

        $totalTime = microtime(true) - $startTime;
        $averageTime = $totalTime / $operations;

        echo "\n併發操作總時間: " . number_format($totalTime * 1000, 2) . "ms";
        echo "\n平均每次操作: " . number_format($averageTime * 1000, 2) . "ms";

        // 驗證併發效能
        $this->assertLessThan(0.05, $averageTime, '併發操作效能不佳');
    }

    /**
     * 測試記憶體使用效率
     */
    public function test_memory_efficiency()
    {
        $initialMemory = memory_get_usage(true);

        // 執行大量操作
        for ($i = 0; $i < 100; $i++) {
            $this->centerService->getAllCenters();
            $this->centerService->searchAccounts('A', 10);
            $this->cacheService->getCenterLevels();
            $this->cacheService->getCenterRoles();
        }

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        echo "\n初始記憶體: " . number_format($initialMemory / 1024 / 1024, 2) . "MB";
        echo "\n最終記憶體: " . number_format($finalMemory / 1024 / 1024, 2) . "MB";
        echo "\n記憶體增長: " . number_format($memoryIncrease / 1024 / 1024, 2) . "MB";

        // 驗證記憶體使用合理
        $this->assertLessThan(5 * 1024 * 1024, $memoryIncrease, '記憶體使用過多');
    }

    /**
     * 測試快取命中率
     */
    public function test_cache_hit_rate()
    {
        Cache::flush();

        $queries = ['A001', 'A002', 'A003', 'A001', 'A002', 'A003'];
        $cacheHits = 0;
        $totalQueries = count($queries);

        foreach ($queries as $query) {
            $cacheKey = "account_search:" . md5($query . '10');
            $hadCache = Cache::has($cacheKey);

            $this->centerService->searchAccounts($query, 10);

            if ($hadCache) {
                $cacheHits++;
            }
        }

        $hitRate = ($cacheHits / $totalQueries) * 100;
        echo "\n快取命中率: " . number_format($hitRate, 1) . "%";

        // 驗證快取命中率合理（重複查詢應該有快取）
        $this->assertGreaterThan(30, $hitRate, '快取命中率過低');
    }

    /**
     * 效能基準測試
     */
    public function test_performance_benchmark()
    {
        $benchmarks = [
            'center_list_no_filter' => function () {
                return $this->centerService->getAllCenters();
            },
            'center_list_with_filter' => function () {
                return $this->centerService->getAllCenters(['status' => 1]);
            },
            'account_search' => function () {
                return $this->centerService->searchAccounts('A001', 10);
            },
            'cache_center_levels' => function () {
                return $this->cacheService->getCenterLevels();
            },
            'cache_center_roles' => function () {
                return $this->cacheService->getCenterRoles();
            },
        ];

        $results = [];

        foreach ($benchmarks as $name => $operation) {
            $times = [];

            // 執行多次測試取平均值
            for ($i = 0; $i < 10; $i++) {
                $startTime = microtime(true);
                $operation();
                $times[] = microtime(true) - $startTime;
            }

            $averageTime = array_sum($times) / count($times);
            $results[$name] = $averageTime;

            echo "\n{$name}: " . number_format($averageTime * 1000, 2) . "ms";
        }

        // 驗證所有操作都在合理時間內完成
        foreach ($results as $name => $time) {
            $this->assertLessThan(0.5, $time, "{$name} 效能不佳");
        }

        return $results;
    }

    /**
     * 壓力測試
     */
    public function test_stress_test()
    {
        $startTime = microtime(true);
        $operations = 0;
        $errors = 0;

        // 執行 5 秒的壓力測試
        while ((microtime(true) - $startTime) < 5) {
            try {
                $this->centerService->getAllCenters();
                $this->centerService->searchAccounts('A00', 5);
                $this->cacheService->getCenterLevels();
                $operations += 3;
            } catch (\Exception $e) {
                $errors++;
            }
        }

        $totalTime = microtime(true) - $startTime;
        $operationsPerSecond = $operations / $totalTime;

        echo "\n壓力測試結果:";
        echo "\n總操作數: {$operations}";
        echo "\n錯誤數: {$errors}";
        echo "\n每秒操作數: " . number_format($operationsPerSecond, 2);
        echo "\n錯誤率: " . number_format(($errors / $operations) * 100, 2) . "%";

        // 驗證系統穩定性
        $this->assertLessThan(0.01, $errors / $operations, '錯誤率過高');
        $this->assertGreaterThan(100, $operationsPerSecond, '吞吐量過低');
    }

    /**
     * 輸出效能報告
     */
    public function test_generate_performance_report()
    {
        echo "\n" . str_repeat("=", 60);
        echo "\n中心管理系統效能測試報告";
        echo "\n" . str_repeat("=", 60);

        // 執行各項測試並收集結果
        $this->test_cache_service_performance();
        $this->test_account_search_performance();
        $this->test_center_list_performance();
        $this->test_concurrent_operations_performance();
        $this->test_memory_efficiency();
        $this->test_cache_hit_rate();

        echo "\n" . str_repeat("=", 60);
        echo "\n基準測試結果:";
        echo "\n" . str_repeat("-", 60);
        $benchmarks = $this->test_performance_benchmark();

        echo "\n" . str_repeat("=", 60);
        echo "\n壓力測試結果:";
        echo "\n" . str_repeat("-", 60);
        $this->test_stress_test();

        echo "\n" . str_repeat("=", 60);
        echo "\n測試完成時間: " . date('Y-m-d H:i:s');
        echo "\n" . str_repeat("=", 60) . "\n";

        $this->assertTrue(true, '效能測試報告生成完成');
    }
}
