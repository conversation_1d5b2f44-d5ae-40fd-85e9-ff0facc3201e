<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class AccountSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // 簡單檢查是否為管理員
        $adminData = session('admin');
        return $adminData && isset($adminData['id']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'query' => 'required|string|min:1|max:100',
            'limit' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'query.required' => '請輸入搜尋關鍵字',
            'query.min' => '搜尋關鍵字至少需要1個字元',
            'query.max' => '搜尋關鍵字不能超過100個字元',
            'limit.integer' => '限制數量格式無效',
            'limit.min' => '限制數量至少為1',
            'limit.max' => '限制數量不能超過100'
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(
                response()->json([
                    'status' => false,
                    'message' => '搜尋參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }

    /**
     * 獲取清理後的搜尋查詢
     */
    public function getCleanQuery(): string
    {
        return trim($this->input('query', ''));
    }

    /**
     * 獲取搜尋限制
     */
    public function getLimit(): int
    {
        return min($this->input('limit', 10), 100);
    }
}
