<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\DB;

class RoleAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // 檢查是否為管理員
        $adminData = session('admin');
        return $adminData && isset($adminData['id']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'center_id' => [
                'required',
                'integer',
                'exists:main_db.centers,id'
            ],
            'account_number' => [
                'required',
                'string',
                'exists:main_db.account,number'
            ],
            'role_code' => [
                'required',
                'string',
                'in:founder,director,executive_director,lecturer,market,sales'
            ],
            'start_at' => [
                'nullable',
                'date',
                'after_or_equal:today'
            ],
            'end_at' => [
                'nullable',
                'date',
                'after:start_at'
            ],
            'note' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'center_id.required' => '中心ID為必填',
            'center_id.integer' => '中心ID格式無效',
            'center_id.exists' => '指定的中心不存在',

            'account_number.required' => '會員編號為必填',
            'account_number.string' => '會員編號格式無效',
            'account_number.exists' => '指定的會員不存在',

            'role_code.required' => '角色代碼為必填',
            'role_code.string' => '角色代碼格式無效',
            'role_code.in' => '無效的角色代碼',

            'start_at.date' => '開始時間格式無效',
            'start_at.after_or_equal' => '開始時間不能早於今天',

            'end_at.date' => '結束時間格式無效',
            'end_at.after' => '結束時間必須晚於開始時間',

            'note.string' => '備註格式無效',
            'note.max' => '備註不能超過255個字元'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'center_id' => '中心',
            'account_id' => '會員',
            'role_code' => '角色',
            'start_at' => '開始時間',
            'end_at' => '結束時間',
            'note' => '備註'
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(
                response()->json([
                    'status' => false,
                    'message' => '資料驗證失敗',
                    'errors' => $validator->errors(),
                    'error_count' => $validator->errors()->count()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            // 額外的業務邏輯驗證
            $this->validateBusinessRules($validator);
        });
    }

    /**
     * 業務邏輯驗證
     */
    protected function validateBusinessRules(Validator $validator): void
    {
        $centerId = $this->input('center_id');
        $accountId = $this->input('account_id');
        $roleCode = $this->input('role_code');

        if (!$centerId || !$accountId || !$roleCode) {
            return; // 基本驗證已經失敗，不需要進行業務邏輯驗證
        }

        // 檢查中心是否啟用
        $center = DB::connection('main_db')
            ->table('centers')
            ->where('id', $centerId)
            ->where('status', 1)
            ->first();

        if (!$center) {
            $validator->errors()->add('center_id', '指定的中心已停用或不存在');
            return;
        }

        // 檢查會員是否啟用
        $account = DB::connection('main_db')
            ->table('account')
            ->where('id', $accountId)
            ->where('status', 1) // 假設有狀態欄位
            ->first();

        if (!$account) {
            $validator->errors()->add('account_id', '指定的會員已停用或不存在');
            return;
        }

        // 檢查角色是否存在且啟用
        $role = DB::connection('main_db')
            ->table('center_roles')
            ->where('code', $roleCode)
            ->where('status', 1) // 假設有狀態欄位
            ->first();

        if (!$role) {
            $validator->errors()->add('role_code', '指定的角色已停用或不存在');
            return;
        }

        // 檢查該會員是否已經在此中心擔任此角色
        $existingRole = DB::connection('main_db')
            ->table('center_staff')
            ->where('center_id', $centerId)
            ->where('account_id', $accountId)
            ->where('role_code', $roleCode)
            ->whereNull('end_at')
            ->first();

        if ($existingRole) {
            $validator->errors()->add('account_id', '該會員已在此中心擔任此角色');
            return;
        }

        // 檢查單例角色衝突（在指派時會自動處理，這裡只是警告）
        if ($role->is_singleton) {
            $existingSingleton = DB::connection('main_db')
                ->table('center_staff')
                ->where('center_id', $centerId)
                ->where('role_code', $roleCode)
                ->whereNull('end_at')
                ->first();

            if ($existingSingleton) {
                // 這不是錯誤，而是會自動處理的情況
                // 可以在這裡記錄日誌或設置警告標記
                \Log::info('單例角色衝突將自動處理', [
                    'center_id' => $centerId,
                    'role_code' => $roleCode,
                    'existing_account_id' => $existingSingleton->account_id,
                    'new_account_id' => $accountId
                ]);
            }
        }

        // 檢查會員是否有權限擔任此角色（可根據業務需求擴展）
        $this->validateRolePermissions($validator, $accountId, $roleCode);
    }

    /**
     * 驗證角色權限
     */
    protected function validateRolePermissions(Validator $validator, int $accountId, string $roleCode): void
    {
        // 這裡可以根據業務需求添加角色權限檢查
        // 例如：檢查會員等級、資格、經驗等

        // 示例：檢查會員是否有足夠的等級擔任管理角色
        $managementRoles = ['founder', 'director', 'executive_director'];

        if (in_array($roleCode, $managementRoles)) {
            $account = DB::connection('main_db')
                ->table('account')
                ->where('id', $accountId)
                ->first();

            if ($account) {
                // 假設有會員等級檢查
                $minLevel = $this->getMinimumLevelForRole($roleCode);
                if (isset($account->level) && $account->level < $minLevel) {
                    $validator->errors()->add('account_id', "該會員等級不足以擔任{$this->getRoleName($roleCode)}");
                }

                // 檢查會員是否已被停權
                if (isset($account->is_banned) && $account->is_banned) {
                    $validator->errors()->add('account_id', '該會員已被停權，無法指派角色');
                }
            }
        }
    }

    /**
     * 獲取角色所需的最低等級
     */
    protected function getMinimumLevelForRole(string $roleCode): int
    {
        $levelRequirements = [
            'founder' => 5,
            'director' => 4,
            'executive_director' => 3,
            'lecturer' => 2,
            'market' => 1,
            'sales' => 1
        ];

        return $levelRequirements[$roleCode] ?? 1;
    }

    /**
     * 獲取角色中文名稱
     */
    protected function getRoleName(string $roleCode): string
    {
        $roleNames = [
            'founder' => '中心發起人',
            'director' => '中心總監',
            'executive_director' => '大總監',
            'lecturer' => '講師',
            'market' => '行政/廣告人員',
            'sales' => '業務'
        ];

        return $roleNames[$roleCode] ?? $roleCode;
    }

    /**
     * 準備驗證資料
     */
    protected function prepareForValidation(): void
    {
        // 清理和標準化輸入資料
        $this->merge([
            'note' => trim($this->input('note', ''))
        ]);

        // 如果沒有提供開始時間，設置為當前時間
        if (!$this->input('start_at')) {
            $this->merge([
                'start_at' => now()->format('Y-m-d H:i:s')
            ]);
        }
    }
}
