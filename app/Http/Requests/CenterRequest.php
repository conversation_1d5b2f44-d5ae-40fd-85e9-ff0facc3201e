<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CenterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // 簡單檢查是否為管理員
        $adminData = session('admin');
        return $adminData && isset($adminData['id']);
    }

    /**
     * 檢查管理員權限
     */
    protected function checkAdminPermissions(int $adminId): bool
    {
        try {
            $admin = DB::connection('main_db')
                ->table('admin')
                ->where('id', $adminId)
                ->where('status', 1)
                ->first();

            if (!$admin) {
                Log::warning('無效的管理員嘗試存取中心管理', [
                    'admin_id' => $adminId
                ]);
                return false;
            }

            // 檢查是否有中心管理權限
            // 這裡可以根據業務需求添加更細緻的權限檢查
            return true;
        } catch (\Exception $e) {
            Log::error('檢查管理員權限時發生錯誤', [
                'admin_id' => $adminId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'name' => [
                'required',
                'string',
                'max:64',
                'regex:/^[\p{L}\p{N}\s\-_()（）]+$/u', // 允許中文、英文、數字、空格、連字符、底線、括號
            ],
            'center_level_id' => [
                'required',
                'integer',
                'exists:main_db.center_level,id'
            ],

        ];

        // 根據請求方法調整規則
        if ($this->isMethod('POST')) {
            // 新增時檢查名稱唯一性
            $rules['name'][] = 'unique:main_db.centers,name';
        } elseif ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            // 更新時排除當前記錄檢查名稱唯一性
            $centerId = $this->route('id') ?? $this->input('id');
            if ($centerId) {
                $rules['name'][] = 'unique:main_db.centers,name,' . $centerId;
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => '中心名稱為必填',
            'name.string' => '中心名稱必須是文字格式',
            'name.max' => '中心名稱不能超過64個字元',
            'name.regex' => '中心名稱只能包含中文、英文、數字、空格、連字符、底線和括號',
            'name.unique' => '中心名稱已存在，請使用其他名稱',

            'center_level_id.required' => '請選擇中心等級',
            'center_level_id.integer' => '中心等級格式無效',
            'center_level_id.exists' => '選擇的中心等級不存在',

            'status.integer' => '狀態格式無效',
            'status.in' => '狀態值必須是0（停用）或1（啟用）'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => '中心名稱',
            'center_level_id' => '中心等級',
            'status' => '狀態'
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new HttpResponseException(
                response()->json([
                    'status' => false,
                    'message' => '資料驗證失敗',
                    'errors' => $validator->errors(),
                    'error_count' => $validator->errors()->count()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            // 額外的業務邏輯驗證
            $this->validateBusinessRules($validator);
        });
    }

    /**
     * 業務邏輯驗證
     */
    protected function validateBusinessRules(Validator $validator): void
    {
        // 檢查中心名稱是否包含敏感詞彙
        $name = $this->input('name');
        if ($name) {
            $forbiddenWords = ['測試', 'test', 'demo', '假', '虛擬', '臨時'];
            foreach ($forbiddenWords as $word) {
                if (stripos($name, $word) === 0) {
                    $validator->errors()->add('name', '中心名稱不能以「' . $word . '」開頭');
                    break;
                }
            }

            // 檢查名稱長度（中文字元計算）
            if (mb_strlen($name, 'UTF-8') < 2) {
                $validator->errors()->add('name', '中心名稱至少需要2個字元');
            }

            // 檢查是否包含特殊字元（除了允許的）
            if (!preg_match('/^[\p{L}\p{N}\s\-_()（）]+$/u', $name)) {
                $validator->errors()->add('name', '中心名稱包含不允許的特殊字元');
            }
        }

        // 檢查中心等級是否啟用
        $centerLevelId = $this->input('center_level_id');
        if ($centerLevelId) {
            $centerLevel = \DB::connection('main_db')
                ->table('center_level')
                ->where('id', $centerLevelId)
                ->first();

            if (!$centerLevel) {
                $validator->errors()->add('center_level_id', '選擇的中心等級不存在');
            } elseif (isset($centerLevel->status) && $centerLevel->status != 1) {
                $validator->errors()->add('center_level_id', '選擇的中心等級已停用');
            }
        }

        // 記錄驗證嘗試
        $adminData = session('admin');
        \Log::info('中心資料驗證', [
            'name' => $name,
            'center_level_id' => $centerLevelId,
            'admin_id' => $adminData['id'] ?? null,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * 準備驗證資料
     */
    protected function prepareForValidation(): void
    {
        // 清理和標準化輸入資料
        $statusValue = $this->has('status') ? $this->input('status') : 1;
        $this->merge([
            'name' => trim($this->input('name', '')),
            'status' => $statusValue, // 正確處理0值
            'center_level_id' => $this->input('center_level_id')
        ]);

        // 記錄資料清理日誌
        $adminData = session('admin');
        Log::info('中心管理請求資料已清理', [
            'admin_id' => $adminData['id'] ?? null,
            'original_data_keys' => array_keys($this->all()),
            'ip' => $this->ip()
        ]);
    }
}
