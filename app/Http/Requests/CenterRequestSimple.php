<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CenterRequestSimple extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // 簡單檢查是否為管理員
        $adminData = session('admin');
        return $adminData && isset($adminData['id']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:64|min:2',
            'center_level_id' => 'required|integer|exists:main_db.center_level,id',
            'status' => 'sometimes|integer|in:0,1'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => '中心名稱為必填項目',
            'name.string' => '中心名稱必須為文字',
            'name.max' => '中心名稱不能超過64個字元',
            'name.min' => '中心名稱至少需要2個字元',
            'center_level_id.required' => '中心等級為必填項目',
            'center_level_id.integer' => '中心等級必須為數字',
            'center_level_id.exists' => '選擇的中心等級不存在',
            'status.integer' => '狀態必須為數字',
            'status.in' => '狀態只能為0或1'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => trim($this->input('name', '')),
            'status' => $this->input('status', 1)
        ]);
    }
}
