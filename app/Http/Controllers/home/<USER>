<?php

namespace app\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Http\Controllers\home\PublicController;
use App\Services\pattern\MemberInstance;
use App\Services\CommonService;

class Ajax extends PublicController
{
    /*取得優惠專區、活動專區選單*/
    public function offerNavMenu(Request $request)
    {
        /*優惠專區選單*/
        $actCond = ['online' => 1];
        $act = DB::table('act')->where($actCond)->get();
        $act = CommonService::objectToArray($act);
        if ($act) {
            foreach ($act as $actKey => $actValue) {
                $act[$actKey]['title'] = $actValue['name'];
            }
        }
        $offerNavMenu['offerMenu'] = $act;

        /*活動專區選單*/
        $onlineactCond = ['online' => 1];
        $onlineact = DB::table('experience')->where($onlineactCond)->get();
        $onlineact = CommonService::objectToArray($onlineact);
        if ($onlineact) {
            foreach ($onlineact as $actKey => $actValue) {
                $onlineact[$actKey]['title'] = $actValue['title'];
            }
        }
        $offerNavMenu['activityMenu'] = $onlineact;

        return $offerNavMenu;
    }

    /*取得「有顯示的」當階及下第一層分類(依傳入的id, action)*/
    public function productMenu(Request $request)
    {
        $category_type = $request->post('category_type');
        $productMenu = parent::getProductMenu($request->post('action'), $request->post('id'), 0, $category_type);
        return $productMenu;
    }

    /*左側商品選單*/
    public function getProdAsideMenu(Request $request)
    {
        $post = $request->post();

        $action = isset($post['action']) ? $post['action'] : "";
        $id = isset($post['id']) ? $post['id'] : "";
        $category_type = isset($post['category_type']) ? $post['category_type'] : null;

        $productMenu = [];
        if ($action == "typeinfo" && $id != "") { /*位於分類，且有提供分類id*/
            $typeinfo = DB::table('typeinfo')->find($id);
            $typeinfo = CommonService::objectToArray($typeinfo);
            if ($typeinfo) {
                /*找出同階的分類*/
                $productMenu = DB::table('typeinfo')->select('title', 'id', 'pic')
                    ->whereRaw("parent_id = " . $typeinfo['parent_id'] . " AND
                                        branch_id = " . $typeinfo['branch_id'] . "
                                            AND online = 1
                                            AND (
                                            end <= 0
                                            OR
                                            (start < " . time() . " AND end > " . time() . ")
                                            )")
                    ->whereRaw('distributor_id="' . $typeinfo['distributor_id'] . '"')
                    ->orderBy('order_id')
                    ->get();
                $productMenu = CommonService::objectToArray($productMenu);
                foreach ($productMenu as $key => $vo) {
                    if ($id == $vo['id']) {
                        $productMenu[$key]['show'] = 'show';
                    }
                    $productMenu[$key]['action'] = 'typeinfo';
                    $subType = DB::table('typeinfo')->select('title', 'id', 'pic')
                        ->whereRaw('branch_id = ' . $vo['id'] . "
                                        AND online = 1
                                        AND (
                                            end <= 0
                                            OR
                                            (start < " . time() . " AND end > " . time() . ")
                                        )")
                        ->whereRaw('distributor_id="' . $typeinfo['distributor_id'] . '"')
                        ->orderBy('order_id')
                        ->get();
                    $subType = CommonService::objectToArray($subType);
                    foreach ($subType as $key2 => $vo2) {
                        if (mb_strlen($subType[$key2]["title"], 'utf8') > 15)
                            $subType[$key2]["title"] = mb_substr($subType[$key2]["title"], 0, 15, 'utf8') . '…';
                    }
                    $productMenu[$key]['subType'] = $subType;
                }
            }
        }

        if ($productMenu == []) { /*到這都還沒設定productMenu，視為位於其他地方(ex:分館)*/
            $distributor_id_where = 'distributor_id=0';
            if (config('control.control_platform') == 1) {
                if ($action == 'distributor') {
                    if ($id != '0') {
                        $MemberInstance = new MemberInstance($id);
                        $user_data = $MemberInstance->get_user_data();
                        // dump($user_data);exit;
                        if ($user_data) {
                            if ($user_data['user_type'] == 1) {
                                $distributor_id_where = 'distributor_id="' . $id . '"';
                            }
                        }
                    }
                } else if ($action == "product" && $id != "") {
                    $product = DB::table('product')->where("id", $id)->first();
                    if (empty($product) == false) {
                        $distributor_id_where = 'distributor_id="' . $product->distributor_id . '"';
                    }
                }
            }
            // dd($distributor_id_where);

            $productMenuQuery = DB::table('product')->select('title', 'id', 'pic_icon as pic', 'distributor_id')
                ->whereRaw('online = 1')
                ->whereRaw($distributor_id_where);

            // 如果指定了 category_type，需要篩選只包含該類型商品的分館
            if ($category_type !== null) {
                $productMenuQuery = $productMenuQuery->whereExists(function ($query) use ($category_type) {
                    $query->select(DB::raw(1))
                        ->from('productinfo')
                        ->whereRaw("productinfo.final_array LIKE CONCAT('%\"prev_id\":\"', product.id, '\"%')")
                        ->where('productinfo.category_type', $category_type)
                        ->where('productinfo.online', 1);
                });
            }

            $productMenu = $productMenuQuery->orderByRaw('order_id asc, id asc')->get();
            $productMenu = CommonService::objectToArray($productMenu);

            foreach ($productMenu as $key => $vo) {
                if ($id == $vo['id']) {
                    $productMenu[$key]['show'] = 'show';
                }
                $productMenu[$key]['action'] = 'product';
                $subType = DB::table('typeinfo')->select('title', 'id', 'pic')
                    ->whereRaw('parent_id = ' . $vo['id'] . "
                                        AND branch_id = 0
                                        AND online = 1
                                        AND (
                                        end <= 0
                                        OR
                                        (start < " . time() . " AND typeinfo.end > " . time() . ")
                                        )")
                    ->where('distributor_id', $vo['distributor_id'])
                    ->orderByRaw('typeinfo.order_id')
                    ->get();
                $subType = CommonService::objectToArray($subType);
                foreach ($subType as $key2 => $vo2) {
                    if (mb_strlen($subType[$key2]["title"], 'utf8') > 15)
                        $subType[$key2]["title"] = mb_substr($subType[$key2]["title"], 0, 15, 'utf8') . '…';
                }
                $productMenu[$key]['subType'] = $subType;
            }
        }

        return $productMenu;
    }

    /*取得最新消息資料*/
    public function newslink(Request $request)
    {
        $newspage = $request->get('newspage');

        $outputData = [
            'online' => intval(DB::table('index_online')->where('id', 1)->value('block_news')),
            'news' => [],
        ];

        if ($outputData['online'] != 0) {
            $db_result = DB::table('news')->select('id', 'title', 'time')->where('online', 1)->orderBy('orders')->orderBy('time', 'desc')->limit(3)->get();
            $db_result = CommonService::objectToArray($db_result);

            if (empty($db_result) == false) {
                foreach ($db_result as $key => $value) {
                    $db_result[$key]['time'] = date("Y-m-d", strtotime($value['time']));
                }

                $outputData['news'] = $db_result;
            }
        }

        return $outputData;
    }

    public function chpwd(Request $request)
    {
        $id = $request->post('id');
        $pwd = $request->post('pwd');

        $MemberInstance = new MemberInstance($id);
        $data = $MemberInstance->get_user_data();

        if ($data['pwd'] == md5($pwd)) {
            return ['status' => true];
        }
        return ['status' => false];
    }

    public function ckaccount(Request $request)
    {
        $phone = $request->post('phone');
        $where = ['a.' . MemberInstance::$account_column => $phone];

        $MemberInstance = new MemberInstance(0);
        $adminData = $MemberInstance->get_user_data($addr_change = "ori", $where);

        ob_clean();
        if ($adminData) {
            echo json_encode(false); // 用戶名已存在
        } else {
            echo json_encode(true); // 用戶名可用
        }
        // return true;
    }

    /*商品問答功能*/
    public function prodAllQa(Request $request)
    {
        $siteName  = config('extra.shop.subDeparment') . '_sub';
        $prod_id = $request->post('prodInfoId');
        $cond = [
            'site_name' => $siteName,
            'prod_id'   => $prod_id,
        ];
        // dump($cond);exit;
        $resData = DB::connection('main_db')->table('product_qa')->where($cond)->orderByRaw('prod_qa_id desc')->get();
        $resData = CommonService::objectToArray($resData);
        foreach ($resData as $key => $value) {
            $resData[$key]['q_datetime'] = $value['q_datetime'] ? date('Y-m-d', strtotime($value['q_datetime'])) : "";
            $resData[$key]['a_datetime'] = $value['a_datetime'] ? date('Y-m-d', strtotime($value['a_datetime'])) : "";
        }
        $this->success($resData);
    }
    public function prodQaCreate(Request $request)
    {
        $prod_q     = $request->post('prodQ');
        if (!$prod_q) {
            $this->error(Lang::get('資料不完整'));
        }

        $siteName  = config('extra.shop.subDeparment') . '_sub';
        $uid        = session('user.id') ?? 0;
        $prod_id    = $request->post('prodInfoId');
        $q_datetime = date('Y-m-d H:i:s');

        if ($uid == 0) {
            $this->error(Lang::get('請先登入會員'));
        }

        /*處理distributor_id*/
        if (config('control.control_platform') == 1) {
            $distributor_id = 0;
            $productinfo = DB::table('productinfo')->where('id', $prod_id)->first();
            $productinfo = CommonService::objectToArray($productinfo);
            if ($productinfo) {
                $distributor_id = $productinfo['distributor_id'];
            }
        } else {
            $distributor_id = 0;
        }

        $insData = array(
            'distributor_id' => $distributor_id,
            'uid'           => $uid,
            'prod_id'       => $prod_id,
            'prod_q'        => str_replace("\n", "<br>", $prod_q),
            'q_datetime'    => $q_datetime,
            'site_name'     => $siteName,
            'prod_addr'     => env('APP_URL') . 'Product/productinfo?id=' . $prod_id,
        );
        DB::connection('main_db')->table('product_qa')->insert($insData);

        $user = DB::connection('main_db')->table('account')->where(['id' => $uid])->first();

        $globalMailData = parent::getMailData();

        $subject = Lang::get('商品問答');
        $product_qa_letter = Lang::get('商品問答信管理者');
        $product_qa_letter = str_replace("{prod_q}", $prod_q, $product_qa_letter);
        $mailBody = "
        <html>
            <head></head>
            <body>
            <div>
                " . $product_qa_letter . "
            </div>
            <div style='color:red;'>
                ≡ " . Lang::get('此信件為系統自動發送，請勿直接回覆') . " ≡
            </div>
            </body>
        </html>
        ";
        if ($distributor_id == 0) {
            $mail_return = parent::Mail_Send($mailBody, 'admin', '', $subject);
        } else {
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();
            // dump($user_data);exit;
            if ($user_data) {
                $mail_return = parent::Mail_Send($mailBody, 'client', $user_data['email'], $subject);
            }
        }
        $this->success('發送成功');
    }

    /*關閉側邊廣告*/
    public function closeAdSide(Request $request)
    {
        session()->put('closeAdSide', 'true');
        // $closeAdSide = session->get('closeAdSide');
        // echo $closeAdSide;
    }

    /*商品加入/取消人氣*/
    public function love_record(Request $request)
    {
        $result = $this->deal_record($request, 'product_love');
        return $result;
    }
    /*商品加入/取消我的收藏*/
    public function store_record(Request $request)
    {
        $result = $this->deal_record($request, 'product_store');
        return $result;
    }
    /*依給定資料表處理紀錄*/
    private function deal_record($request, $tableName)
    {
        $post = $request->post();
        $prodInfoId = isset($post['prodInfoId']) ? $post['prodInfoId'] : null;
        if (!$prodInfoId) {
            $this->error(Lang::get('資料不完整'));
        }
        $status = isset($post['status']) ? $post['status'] : null;
        if ($status === null) {
            $this->error(Lang::get('資料不完整'));
        }
        $user_id = session()->get('user.id');
        if (!$user_id) {
            $this->error(Lang::get('請先登入會員'));
        }

        if ($status == 0) { /*取消*/
            DB::table($tableName)->where('product_id', $prodInfoId)->where('user_id', $user_id)->delete();
        } else { /*加入*/
            $has_loved = DB::table($tableName)->where('product_id', $prodInfoId)->where('user_id', $user_id)->get();
            $has_loved = CommonService::objectToArray($has_loved);
            if (empty($has_loved) == true) {
                DB::table($tableName)->insert([
                    'user_id' => $user_id,
                    'product_id' => $prodInfoId,
                ]);
            }
        }
        $love_num = DB::table($tableName)->where('product_id', $prodInfoId)->count();
        $this->success($love_num);
    }

    /*展示icon*/
    public function icon(Request $request)
    {
        return view('home.ajax.icon', ['data' => $this->data]);
    }
}
