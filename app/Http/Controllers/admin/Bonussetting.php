<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\BonusSettingHelper;

class Bonussetting extends MainController
{
    public function index(Request $request)
    {
        $this->data['bonus_setting'] = BonusSettingHelper::get_bonus_setting();
        return view('admin.bonussetting.index', ['data' => $this->data]);
    }

    public function save_data(Request $request)
    {
        $bonus_setting_update = []; /* id=>value */
        // dd($request->post());
        $cv_rate = $request->post('cv_rate') ?? null;
        if ($cv_rate !== null) {
            if ($cv_rate < 0 || $cv_rate > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['2'] = $cv_rate;
        }
        // 已廢棄..
        // $ad_partner_rate = $request->post('ad_partner_rate') ?? null;
        // if ($ad_partner_rate !== null) {
        //     if ($ad_partner_rate < 0 || $ad_partner_rate > 100) {
        //         $this->error('%數應介於0~100');
        //     }
        //     $bonus_setting_update['3'] = $ad_partner_rate;
        // }
        $limit_c_rate = $request->post('limit_c_rate') ?? null;
        if ($limit_c_rate !== null) {
            if ($limit_c_rate < 0 || $limit_c_rate > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['4'] = $limit_c_rate;
        }
        $limit_o_rate = $request->post('limit_o_rate') ?? null;
        if ($limit_o_rate !== null) {
            if ($limit_o_rate < 0 || $limit_o_rate > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['5'] = $limit_o_rate;
        }
        $divided_times = $request->post('divided_times') ?? null;
        if ($divided_times !== null) {
            $divided_times = explode(',', $divided_times);
            $num = 0;
            foreach ($divided_times as $key => $value) {
                $divided_times[$key] = (float)$value;
                $num += $divided_times[$key];
                if ($value < 0 || $value > 100) {
                    $this->error('%數應介於0~100');
                }
            }
            if ($num < 0 || $num > 100) {
                $this->error('%數加總應介於0~100');
            }
            $bonus_setting_update['6'] = json_encode($divided_times);
        }
        $recommend_times = $request->post('recommend_times') ?? null;
        if ($recommend_times !== null) {
            $recommend_times = explode(',', $recommend_times);
            $num = 0;
            foreach ($recommend_times as $key => $value) {
                $recommend_times[$key] = (float)$value;
                $num += $recommend_times[$key];
                if ($value < 0 || $value > 100) {
                    $this->error('%數應介於0~100');
                }
            }
            if ($num < 0 || $num > 100) {
                $this->error('%數加總應介於0~100');
            }
            $bonus_setting_update['7'] = json_encode($recommend_times);
        }

        $charge_tax = $request->post('charge_tax') ?? null;
        if ($charge_tax !== null) {
            if ($charge_tax < 0 || $charge_tax > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['8'] = $charge_tax;
        }
        $charge_pool = $request->post('charge_pool') ?? null;
        if ($charge_pool !== null) {
            if ($charge_pool < 0 || $charge_pool > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['9'] = $charge_pool;
        }
        $charge_num = (float)($charge_tax ?? 0) + (float)($charge_pool ?? 0);
        if ($charge_num < 0 || $charge_num > 100) {
            $this->error('「提現-代扣稅費」與「提現-轉入資金池」%數加總應介於0~100');
        }

        $month_point2cash = $request->post('month_point2cash') ?? null;
        if ($month_point2cash !== null) {
            if ($month_point2cash < 1) {
                $this->error('月數應大於等於1');
            }
            $bonus_setting_update['10'] = (int)$month_point2cash;
        }
        $month_limit2zero = $request->post('month_limit2zero') ?? null;
        if ($month_limit2zero !== null) {
            if ($month_limit2zero < 1) {
                $this->error('月數應大於等於1');
            }
            $bonus_setting_update['11'] = (int)$month_limit2zero;
        }

        $month_weight_partner = $request->post('month_weight_partner') ?? null;
        if ($month_weight_partner !== null) {
            if ($month_weight_partner < 0 || $month_weight_partner > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['12'] = $month_weight_partner;
        }
        $month_weight_gv_num = $request->post('month_weight_gv_num') ?? null;
        if ($month_weight_gv_num !== null) {
            if ($month_weight_gv_num <= 0) {
                $this->error('數字應為大於零的數');
            }
            $bonus_setting_update['13'] = $month_weight_gv_num;
        }
        $month_weight_gv = $request->post('month_weight_gv') ?? null;
        if ($month_weight_gv !== null) {
            if ($month_weight_gv < 0 || $month_weight_gv > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['14'] = $month_weight_gv;
        }
        $member_transfer_point_increasable = $request->post('member_transfer_point_increasable') ?? null;
        if ($member_transfer_point_increasable !== null) {
            if ($member_transfer_point_increasable < 0 || $member_transfer_point_increasable > 1) {
                $this->error('會員轉移增值積分僅可設定開放或關閉');
            }
            $bonus_setting_update['15'] = $member_transfer_point_increasable;
        }
        $member_point_to_cash = $request->post('member_point_to_cash') ?? null;
        if ($member_point_to_cash !== null) {
            if ($member_point_to_cash < 0 || $member_point_to_cash > 1) {
                $this->error('會員現金積分提現僅可設定開放或關閉');
            }
            $bonus_setting_update['16'] = $member_point_to_cash;
        }

        // 新增：CV 分配比例設定
        $cv_distribution_push_ratio = $request->post('cv_distribution_push_ratio') ?? null;
        if ($cv_distribution_push_ratio !== null) {
            if ($cv_distribution_push_ratio < 0 || $cv_distribution_push_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['17'] = $cv_distribution_push_ratio;
        }
        $cv_distribution_marketing_dept_ratio = $request->post('cv_distribution_marketing_dept_ratio') ?? null;
        if ($cv_distribution_marketing_dept_ratio !== null) {
            if ($cv_distribution_marketing_dept_ratio < 0 || $cv_distribution_marketing_dept_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['18'] = $cv_distribution_marketing_dept_ratio;
        }
        $cv_distribution_sales_dept_ratio = $request->post('cv_distribution_sales_dept_ratio') ?? null;
        if ($cv_distribution_sales_dept_ratio !== null) {
            if ($cv_distribution_sales_dept_ratio < 0 || $cv_distribution_sales_dept_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['19'] = $cv_distribution_sales_dept_ratio;
        }
        $cv_distribution_center_ratio = $request->post('cv_distribution_center_ratio') ?? null;
        if ($cv_distribution_center_ratio !== null) {
            if ($cv_distribution_center_ratio < 0 || $cv_distribution_center_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['20'] = $cv_distribution_center_ratio;
        }

        // 比例加總檢查：推三反一 + 行政 + 業務 + 中心 必須等於 100%
        $push = (float)($request->post('cv_distribution_push_ratio') ?? 0);
        $mkt = (float)($request->post('cv_distribution_marketing_dept_ratio') ?? 0);
        $sales = (float)($request->post('cv_distribution_sales_dept_ratio') ?? 0);
        $center = (float)($request->post('cv_distribution_center_ratio') ?? 0);
        $sum4 = $push + $mkt + $sales + $center;
        if ($request->hasAny(['cv_distribution_push_ratio', 'cv_distribution_marketing_dept_ratio', 'cv_distribution_sales_dept_ratio', 'cv_distribution_center_ratio'])) {
            if (abs($sum4 - 100.0) > 0.001) {
                $this->error('推三反一與行政/業務/中心的比例加總需為 100%。目前加總為：' . $sum4 . '%');
            }
        }

        // 廣告(CV)分配：新增可設定欄位（全部為 0–100 數值）
        $ad_distribution_partner_ratio = $request->post('ad_distribution_partner_ratio') ?? null;
        if ($ad_distribution_partner_ratio !== null) {
            if ($ad_distribution_partner_ratio < 0 || $ad_distribution_partner_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['21'] = $ad_distribution_partner_ratio;
        }
        $ad_distribution_marketing_dept_ratio = $request->post('ad_distribution_marketing_dept_ratio') ?? null;
        if ($ad_distribution_marketing_dept_ratio !== null) {
            if ($ad_distribution_marketing_dept_ratio < 0 || $ad_distribution_marketing_dept_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['22'] = $ad_distribution_marketing_dept_ratio;
        }
        $ad_distribution_sales_dept_ratio = $request->post('ad_distribution_sales_dept_ratio') ?? null;
        if ($ad_distribution_sales_dept_ratio !== null) {
            if ($ad_distribution_sales_dept_ratio < 0 || $ad_distribution_sales_dept_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['23'] = $ad_distribution_sales_dept_ratio;
        }
        $ad_distribution_center_ratio = $request->post('ad_distribution_center_ratio') ?? null;
        if ($ad_distribution_center_ratio !== null) {
            if ($ad_distribution_center_ratio < 0 || $ad_distribution_center_ratio > 100) {
                $this->error('%數應介於0~100');
            }
            $bonus_setting_update['24'] = $ad_distribution_center_ratio;
        }

        // 加總檢查：廣告分配四項合計需等於 100
        if ($request->hasAny(['ad_distribution_partner_ratio', 'ad_distribution_marketing_dept_ratio', 'ad_distribution_sales_dept_ratio', 'ad_distribution_center_ratio'])) {
            $ad_sum4 = (float)($request->post('ad_distribution_partner_ratio') ?? 0)
                + (float)($request->post('ad_distribution_marketing_dept_ratio') ?? 0)
                + (float)($request->post('ad_distribution_sales_dept_ratio') ?? 0)
                + (float)($request->post('ad_distribution_center_ratio') ?? 0);
            if (abs($ad_sum4 - 100.0) > 0.001) {
                $this->error('廣告分配比例（合夥人/行政/業務/中心）加總需為 100%。目前加總為：' . $ad_sum4 . '%');
            }
        }

        if ($bonus_setting_update) {
            foreach ($bonus_setting_update as $id => $value) {
                DB::connection('main_db')->table('bonus_setting')
                    ->where('id', $id)
                    ->update(['value' => $value]);
            }
        }
        $this->success('操作成功');
    }
}
