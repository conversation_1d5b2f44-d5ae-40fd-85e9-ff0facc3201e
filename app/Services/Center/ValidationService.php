<?php

namespace App\Services\Center;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ValidationService
{
    /**
     * 驗證中心資料
     */
    public function validateCenterData(array $data, ?int $centerId = null): array
    {
        $rules = [
            'name' => [
                'required',
                'string',
                'max:64',
                'regex:/^[\p{L}\p{N}\s\-_()（）]+$/u',
            ],
            'center_level_id' => [
                'required',
                'integer',
                'exists:main_db.center_level,id'
            ],
            'status' => [
                'nullable',
                'integer',
                'in:0,1'
            ]
        ];

        // 處理名稱唯一性驗證
        if ($centerId) {
            $rules['name'][] = 'unique:main_db.centers,name,' . $centerId;
        } else {
            $rules['name'][] = 'unique:main_db.centers,name';
        }

        $messages = [
            'name.required' => '中心名稱為必填',
            'name.string' => '中心名稱必須是文字格式',
            'name.max' => '中心名稱不能超過64個字元',
            'name.regex' => '中心名稱只能包含中文、英文、數字、空格、連字符、底線和括號',
            'name.unique' => '中心名稱已存在，請使用其他名稱',
            'center_level_id.required' => '請選擇中心等級',
            'center_level_id.integer' => '中心等級格式無效',
            'center_level_id.exists' => '選擇的中心等級不存在',
            'status.integer' => '狀態格式無效',
            'status.in' => '狀態值必須是0（停用）或1（啟用）'
        ];

        $validator = Validator::make($data, $rules, $messages);

        // 額外的業務邏輯驗證
        $validator->after(function ($validator) use ($data) {
            $this->validateCenterBusinessRules($validator, $data);
        });

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * 驗證角色指派資料
     */
    public function validateRoleAssignmentData(array $data): array
    {
        $rules = [
            'center_id' => [
                'required',
                'integer',
                'exists:main_db.centers,id'
            ],
            'account_id' => [
                'required',
                'integer',
                'exists:main_db.account,id'
            ],
            'role_code' => [
                'required',
                'string',
                'in:founder,director,executive_director,lecturer,market,sales'
            ],
            'start_at' => [
                'nullable',
                'date',
                'after_or_equal:today'
            ],
            'note' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];

        $messages = [
            'center_id.required' => '中心ID為必填',
            'center_id.exists' => '指定的中心不存在',
            'account_id.required' => '會員ID為必填',
            'account_id.exists' => '指定的會員不存在',
            'role_code.required' => '角色代碼為必填',
            'role_code.in' => '無效的角色代碼',
            'start_at.date' => '開始時間格式無效',
            'start_at.after_or_equal' => '開始時間不能早於今天',
            'note.max' => '備註不能超過255個字元'
        ];

        $validator = Validator::make($data, $rules, $messages);

        // 額外的業務邏輯驗證
        $validator->after(function ($validator) use ($data) {
            $this->validateRoleAssignmentBusinessRules($validator, $data);
        });

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * 驗證會員搜尋資料
     */
    public function validateAccountSearchData(array $data): array
    {
        $rules = [
            'query' => 'required|string|min:1|max:100',
            'limit' => 'nullable|integer|min:1|max:100'
        ];

        $messages = [
            'query.required' => '搜尋關鍵字為必填',
            'query.min' => '搜尋關鍵字至少需要1個字元',
            'query.max' => '搜尋關鍵字不能超過100個字元',
            'limit.integer' => '限制數量格式無效',
            'limit.min' => '限制數量至少為1',
            'limit.max' => '限制數量不能超過100'
        ];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * 中心業務邏輯驗證
     */
    protected function validateCenterBusinessRules($validator, array $data): void
    {
        $name = $data['name'] ?? '';
        $centerLevelId = $data['center_level_id'] ?? null;

        // 檢查中心名稱是否包含敏感詞彙
        if ($name) {
            $forbiddenWords = ['測試', 'demo', '假', '虛擬', '臨時'];
            foreach ($forbiddenWords as $word) {
                if (stripos($name, $word) === 0) {
                    $validator->errors()->add('name', '中心名稱不能以「' . $word . '」開頭');
                    break;
                }
            }

            // 檢查名稱長度（中文字元計算）
            if (mb_strlen($name, 'UTF-8') < 2) {
                $validator->errors()->add('name', '中心名稱至少需要2個字元');
            }
        }

        // 檢查中心等級是否啟用
        if ($centerLevelId) {
            $centerLevel = DB::connection('main_db')
                ->table('center_level')
                ->where('id', $centerLevelId)
                ->first();

            if (!$centerLevel) {
                $validator->errors()->add('center_level_id', '選擇的中心等級不存在');
            } elseif (isset($centerLevel->status) && $centerLevel->status != 1) {
                $validator->errors()->add('center_level_id', '選擇的中心等級已停用');
            }
        }
    }

    /**
     * 角色指派業務邏輯驗證
     */
    protected function validateRoleAssignmentBusinessRules($validator, array $data): void
    {
        $centerId = $data['center_id'] ?? null;
        $accountId = $data['account_id'] ?? null;
        $roleCode = $data['role_code'] ?? null;

        if (!$centerId || !$accountId || !$roleCode) {
            return;
        }

        // 檢查中心是否啟用
        $center = DB::connection('main_db')
            ->table('centers')
            ->where('id', $centerId)
            ->first();

        if (!$center) {
            $validator->errors()->add('center_id', '指定的中心不存在');
            return;
        }

        if (isset($center->status) && $center->status != 1) {
            $validator->errors()->add('center_id', '指定的中心已停用');
            return;
        }

        // 檢查會員是否存在且啟用
        $account = DB::connection('main_db')
            ->table('account')
            ->where('id', $accountId)
            ->first();

        if (!$account) {
            $validator->errors()->add('account_id', '指定的會員不存在');
            return;
        }

        // 檢查角色是否存在
        $role = DB::connection('main_db')
            ->table('center_roles')
            ->where('code', $roleCode)
            ->first();

        if (!$role) {
            $validator->errors()->add('role_code', '指定的角色不存在');
            return;
        }

        // 檢查該會員是否已經在此中心擔任此角色
        $existingRole = DB::connection('main_db')
            ->table('center_staff')
            ->where('center_id', $centerId)
            ->where('account_id', $accountId)
            ->where('role_code', $roleCode)
            ->whereNull('end_at')
            ->first();

        if ($existingRole) {
            $validator->errors()->add('account_id', '該會員已在此中心擔任此角色');
        }
    }

    /**
     * 搜尋安全性驗證
     */
    protected function validateSearchSecurity($validator, array $data): void
    {
        $query = $data['query'] ?? '';

        if (!$query) {
            return;
        }

        // 檢查是否包含危險字元或SQL注入嘗試
        $dangerousPatterns = [
            '/\b(select|insert|update|delete|drop|create|alter|exec|execute)\b/i',
            '/[<>"\']/',
            '/\b(script|javascript|vbscript)\b/i',
            '/\b(union|or|and)\s+\d+\s*=\s*\d+/i',
            '/\-\-/',
            '/\/\*.*\*\//',
            '/\bxp_\w+/i'
        ];

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $query)) {
                $validator->errors()->add('query', '搜尋關鍵字包含不安全的內容');
                Log::warning('檢測到可疑的搜尋查詢', [
                    'query' => $query,
                    'admin_id' => session('admin_id'),
                    'ip' => request()->ip(),
                    'user_agent' => request()->userAgent()
                ]);
                break;
            }
        }
    }

    /**
     * 驗證中心刪除權限
     */
    public function validateCenterDeletion(int $centerId): array
    {
        $result = [
            'can_delete' => false,
            'message' => '',
            'active_staff_count' => 0,
            'warnings' => []
        ];

        try {
            // 檢查中心是否存在
            $center = DB::connection('main_db')
                ->table('centers')
                ->where('id', $centerId)
                ->first();

            if (!$center) {
                $result['message'] = '指定的中心不存在';
                return $result;
            }

            // 檢查是否有現役人員
            $activeStaffCount = DB::connection('main_db')
                ->table('center_staff')
                ->where('center_id', $centerId)
                ->whereNull('end_at')
                ->count();

            $result['active_staff_count'] = $activeStaffCount;

            if ($activeStaffCount > 0) {
                $result['message'] = "無法刪除：中心「{$center->name}」仍有 {$activeStaffCount} 位現役人員";
                $result['warnings'][] = '請先移除所有現役人員後再進行刪除';
            } else {
                $result['can_delete'] = true;
                $result['message'] = '可以刪除此中心';
            }

            // 檢查是否有歷史記錄
            $historyCount = DB::connection('main_db')
                ->table('center_staff')
                ->where('center_id', $centerId)
                ->whereNotNull('end_at')
                ->count();

            if ($historyCount > 0) {
                $result['warnings'][] = "此中心有 {$historyCount} 筆歷史人員記錄，刪除後將無法復原";
            }
        } catch (\Exception $e) {
            Log::error('驗證中心刪除權限時發生錯誤', [
                'center_id' => $centerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $result['message'] = '檢查刪除權限時發生錯誤';
        }

        return $result;
    }

    /**
     * 清理和標準化輸入資料
     */
    public function sanitizeInput(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // 移除前後空白
                $value = trim($value);

                // 移除多餘的空格
                $value = preg_replace('/\s+/', ' ', $value);

                // HTML 實體編碼（防止 XSS）
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');

                $sanitized[$key] = $value;
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInput($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * 格式化驗證錯誤訊息
     */
    public function formatValidationErrors(\Illuminate\Support\MessageBag $errors): array
    {
        $formatted = [];

        foreach ($errors->getMessages() as $field => $messages) {
            $formatted[$field] = [
                'field' => $field,
                'messages' => $messages,
                'first_message' => $messages[0] ?? '',
                'count' => count($messages)
            ];
        }

        return $formatted;
    }

    /**
     * 記錄驗證失敗日誌
     */
    public function logValidationFailure(string $operation, array $data, \Illuminate\Support\MessageBag $errors): void
    {
        Log::warning('資料驗證失敗', [
            'operation' => $operation,
            'admin_id' => session('admin_id'),
            'admin_name' => session('admin_name'),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $this->sanitizeLogData($data),
            'errors' => $errors->toArray(),
            'timestamp' => now()
        ]);
    }

    /**
     * 清理日誌資料（移除敏感資訊）
     */
    protected function sanitizeLogData(array $data): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key'];
        $sanitized = $data;

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '***';
            }
        }

        return $sanitized;
    }
}
