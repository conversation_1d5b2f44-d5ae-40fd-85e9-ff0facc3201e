<?php

namespace App\Services\Center;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * CenterCacheService
 *
 * 中心管理快取服務，提供高效的資料快取機制
 */
class CenterCacheService
{
    const CACHE_TTL = 3600; // 1小時
    const CACHE_PREFIX = 'center_management:';

    /**
     * 取得快取的中心等級列表
     */
    public function getCenterLevels(): Collection
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'center_levels',
            self::CACHE_TTL,
            function () {
                return DB::connection('main_db')
                    ->table('center_level')
                    ->select('id', 'name', 'status')
                    ->where('status', 1)
                    ->orderBy('id')
                    ->get();
            }
        );
    }

    /**
     * 取得快取的角色列表
     */
    public function getCenterRoles(): Collection
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'center_roles',
            self::CACHE_TTL,
            function () {
                return DB::connection('main_db')
                    ->table('center_roles')
                    ->select('id', 'code', 'name', 'is_singleton')
                    ->orderBy('id')
                    ->get();
            }
        );
    }

    /**
     * 取得快取的中心統計資訊
     */
    public function getCenterStats(int $centerId): array
    {
        return Cache::remember(
            self::CACHE_PREFIX . "center_stats:{$centerId}",
            300, // 5分鐘快取
            function () use ($centerId) {
                $activeStaffCount = DB::connection('main_db')
                    ->table('center_staff')
                    ->where('center_id', $centerId)
                    ->whereNull('end_at')
                    ->count();

                $totalStaffCount = DB::connection('main_db')
                    ->table('center_staff')
                    ->where('center_id', $centerId)
                    ->count();

                $roleStats = DB::connection('main_db')
                    ->table('center_staff as cs')
                    ->join('center_roles as cr', 'cs.role_code', '=', 'cr.code')
                    ->where('cs.center_id', $centerId)
                    ->whereNull('cs.end_at')
                    ->select('cr.code', 'cr.name', 'cr.is_singleton', DB::raw('COUNT(*) as count'))
                    ->groupBy('cr.code', 'cr.name', 'cr.is_singleton')
                    ->get()
                    ->toArray();

                return [
                    'active_staff_count' => $activeStaffCount,
                    'total_staff_count' => $totalStaffCount,
                    'role_stats' => $roleStats,
                ];
            }
        );
    }

    /**
     * 清除中心相關快取
     */
    public function clearCenterCache(int $centerId): void
    {
        Cache::forget(self::CACHE_PREFIX . "center_stats:{$centerId}");
        Cache::forget(self::CACHE_PREFIX . "center_detail:{$centerId}");

        // 清除列表快取（因為可能影響統計）
        $this->clearListCache();
    }

    /**
     * 清除列表相關快取
     */
    public function clearListCache(): void
    {
        // 清除可能的分頁快取
        $cacheKeys = [
            self::CACHE_PREFIX . 'center_list:*',
            self::CACHE_PREFIX . 'center_search:*'
        ];

        foreach ($cacheKeys as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * 清除基礎資料快取
     */
    public function clearMasterDataCache(): void
    {
        Cache::forget(self::CACHE_PREFIX . 'center_levels');
        Cache::forget(self::CACHE_PREFIX . 'center_roles');
    }

    /**
     * 清除所有中心管理相關快取
     */
    public function clearAllCache(): void
    {
        $this->clearCacheByPattern(self::CACHE_PREFIX . '*');
    }

    /**
     * 根據模式清除快取
     */
    private function clearCacheByPattern(string $pattern): void
    {
        // 這裡需要根據快取驅動實作
        // Redis 可以使用 KEYS 命令
        // 其他驅動可能需要不同的實作方式

        if (config('cache.default') === 'redis') {
            $redis = Cache::getRedis();
            $keys = $redis->keys($pattern);
            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
        // 對於其他快取驅動，可以考慮使用標籤或其他方式
    }

    /**
     * 預熱快取
     */
    public function warmupCache(): void
    {
        // 預載基礎資料
        $this->getCenterLevels();
        $this->getCenterRoles();

        // 預載熱門中心的統計資料
        $popularCenters = DB::connection('main_db')
            ->table('centers')
            ->where('status', 1)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->pluck('id');

        foreach ($popularCenters as $centerId) {
            $this->getCenterStats($centerId);
        }
    }

    /**
     * 取得快取統計資訊
     */
    public function getCacheStats(): array
    {
        $stats = [
            'center_levels' => Cache::has(self::CACHE_PREFIX . 'center_levels'),
            'center_roles' => Cache::has(self::CACHE_PREFIX . 'center_roles'),
            'cached_centers' => 0,
        ];

        // 計算已快取的中心數量（這需要根據快取驅動調整）
        if (config('cache.default') === 'redis') {
            $redis = Cache::getRedis();
            $keys = $redis->keys(self::CACHE_PREFIX . 'center_stats:*');
            $stats['cached_centers'] = count($keys);
        }

        return $stats;
    }
}
