<?php

namespace App\Services\Center;

use App\Models\Main\Center;
use App\Models\Main\CenterLevel;
use App\Models\Main\Account;
use App\Models\Main\CenterStaff;
use App\Services\Center\ValidationService;
use App\Services\Center\CenterCacheService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;

/**
 * CenterService
 *
 * 中心管理服務類別，提供中心的 CRUD 操作和相關業務邏輯
 *
 * 功能：
 * - 中心的 CRUD 操作
 * - 中心刪除前的安全檢查
 * - 會員搜尋和自動完成
 * - 資料驗證和業務邏輯處理
 */
class CenterService
{
    protected $validationService;
    protected $cacheService;

    public function __construct()
    {
        $this->validationService = new ValidationService();
        $this->cacheService = new CenterCacheService();
    }
    /**
     * 取得所有中心列表，支援篩選和分頁
     * 優化版本：使用索引和查詢優化
     *
     * @param array $filters 篩選條件
     * @param int $perPage 每頁數量
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAllCenters(array $filters = [], int $perPage = 15)
    {
        $query = Center::query()
            ->select([
                'centers.id',
                'centers.name',
                'centers.center_level_id',
                'centers.status',
                'centers.created_at',
                'centers.updated_at'
            ])
            ->with(['level:id,name'])
            ->withCount(['activeStaff as active_staff_count']);

        // 優化搜尋條件順序，先處理索引效率高的條件

        // 狀態篩選（使用索引）
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('centers.status', $filters['status']);
        }

        // 等級篩選（使用複合索引）
        if (!empty($filters['center_level_id'])) {
            $query->where('centers.center_level_id', $filters['center_level_id']);
        }

        // 名稱搜尋（使用複合索引）
        if (!empty($filters['name'])) {
            $query->where('centers.name', 'like', '%' . $filters['name'] . '%');
        }

        // 使用索引排序
        return $query->orderBy('centers.created_at', 'desc')->paginate($perPage);
    }

    /**
     * 建立新中心
     *
     * @param array $data 中心資料
     * @return Center
     * @throws ValidationException
     */
    public function createCenter(array $data): Center
    {
        // 驗證資料
        $validatedData = $this->validateCenterData($data);

        return DB::connection('main_db')->transaction(function () use ($validatedData) {
            $center = Center::create([
                'name' => $validatedData['name'],
                'center_level_id' => $validatedData['center_level_id'],
                'status' => $validatedData['status'] ?? 1,
            ]);

            // 清除相關快取
            $this->cacheService->clearListCache();

            return $center->load(['level', 'activeStaff.role', 'activeStaff.account']);
        });
    }

    /**
     * 更新中心資料
     *
     * @param int $id 中心ID
     * @param array $data 更新資料
     * @return Center
     * @throws ModelNotFoundException|ValidationException
     */
    public function updateCenter(int $id, array $data): Center
    {
        $center = Center::findOrFail($id);

        // 驗證資料
        $validatedData = $this->validateCenterData($data, $id);

        return DB::connection('main_db')->transaction(function () use ($center, $validatedData, $id) {
            $center->update([
                'name' => $validatedData['name'],
                'center_level_id' => $validatedData['center_level_id'],
                'status' => $validatedData['status'] ?? $center->status,
            ]);

            // 清除相關快取
            $this->cacheService->clearCenterCache($id);
            $this->cacheService->clearListCache();

            return $center->load(['level', 'activeStaff.role', 'activeStaff.account']);
        });
    }

    /**
     * 刪除中心
     * 實作刪除前的安全檢查和交易處理
     * 需求: 1.5, 1.6
     *
     * @param int $id 中心ID
     * @return bool
     * @throws ModelNotFoundException|\RuntimeException
     */
    public function deleteCenter(int $id): bool
    {
        $center = Center::findOrFail($id);

        // 檢查是否可以刪除（安全檢查）
        if (!$this->canDeleteCenter($id)) {
            $activeStaffCount = CenterStaff::query()
                ->where('center_id', $id)
                ->active()
                ->count();

            throw new \RuntimeException("無法刪除：中心「{$center->name}」仍有 {$activeStaffCount} 位現役人員");
        }

        return DB::connection('main_db')->transaction(function () use ($center, $id) {
            // 記錄刪除前的中心資訊
            \Log::info('準備刪除中心', [
                'center_id' => $id,
                'center_name' => $center->name,
                'center_level_id' => $center->center_level_id,
                'status' => $center->status,
                'created_at' => $center->created_at
            ]);

            // 執行刪除
            $deleted = $center->delete();

            if ($deleted) {
                \Log::info('中心刪除成功（服務層）', [
                    'center_id' => $id,
                    'center_name' => $center->name
                ]);

                // 清除相關快取
                $this->cacheService->clearCenterCache($id);
                $this->cacheService->clearListCache();
            }

            return $deleted;
        });
    }

    /**
     * 檢查中心是否可以刪除
     *
     * 需求 1.5: 檢查該中心是否有任何現役人員
     *
     * @param int $id 中心ID
     * @return bool
     */
    public function canDeleteCenter(int $id): bool
    {
        return !CenterStaff::query()
            ->where('center_id', $id)
            ->active()
            ->exists();
    }

    /**
     * 搜尋會員，支援自動完成
     * 優化版本：使用索引和快取
     *
     * 需求 5.1, 5.2: 從 account 表的 number 欄位進行模糊搜尋
     *
     * @param string $query 搜尋關鍵字
     * @param int $limit 限制結果數量
     * @return Collection
     */
    public function searchAccounts(string $query, int $limit = 10): Collection
    {
        $cleanQuery = trim($query);
        $safeLimit = min($limit, 100);

        if (empty($cleanQuery)) {
            return collect();
        }

        try {
            // 直接搜尋，支援模糊搜尋，使用DB查詢確保連接正確
            $accounts = DB::connection('main_db')->table('account')
                ->where('number', 'like', '%' . $cleanQuery . '%')
                ->where('status', 1)
                ->select(['id', 'number', 'name', 'email', 'status'])
                ->limit($safeLimit)
                ->orderBy('number')
                ->get();

            return $accounts->map(function ($account) {
                return [
                    'id' => $account->id,
                    'number' => $account->number,
                    'name' => $account->name ?? '',
                    'email' => $account->email ?? '',
                    'status' => $account->status ?? 1,
                    'display' => $account->number . ($account->name ? " ({$account->name})" : ''),
                ];
            });
        } catch (\Exception $e) {
            \Log::error('會員搜尋失敗', [
                'query' => $cleanQuery,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return collect();
        }
    }

    /**
     * 取得中心詳細資訊，包含所有人員
     *
     * @param int $id 中心ID
     * @return Center
     * @throws ModelNotFoundException
     */
    public function getCenterWithStaff(int $id): Center
    {
        return Center::query()
            ->with([
                'level',
                'staff.role',
                'staff.account',
                'activeStaff.role',
                'activeStaff.account'
            ])
            ->findOrFail($id);
    }

    /**
     * 取得所有可用的中心等級
     * 使用快取優化
     *
     * @return Collection
     */
    public function getAllCenterLevels(): Collection
    {
        return $this->cacheService->getCenterLevels();
    }

    /**
     * 驗證中心資料
     *
     * @param array $data 待驗證資料
     * @param int|null $excludeId 排除的中心ID（用於更新時）
     * @return array 驗證後的資料
     * @throws ValidationException
     */
    public function validateCenterData(array $data, ?int $excludeId = null): array
    {
        $errors = [];

        // 驗證中心名稱
        if (empty($data['name'])) {
            $errors['name'] = '中心名稱為必填';
        } elseif (strlen($data['name']) > 64) {
            $errors['name'] = '中心名稱不能超過64個字元';
        } else {
            // 檢查名稱是否重複
            $query = Center::query()->where('name', $data['name']);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            if ($query->exists()) {
                $errors['name'] = '中心名稱已存在';
            }
        }

        // 驗證中心等級
        if (empty($data['center_level_id'])) {
            $errors['center_level_id'] = '請選擇中心等級';
        } elseif (!CenterLevel::query()->where('id', $data['center_level_id'])->exists()) {
            $errors['center_level_id'] = '選擇的中心等級不存在';
        }

        // 驗證狀態 - 已簡化，不進行驗證
        // if (isset($data['status']) && !in_array((int)$data['status'], [0, 1])) {
        //     $errors['status'] = '請選擇有效的選項';
        // }

        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

        return [
            'name' => trim($data['name']),
            'center_level_id' => (int) $data['center_level_id'],
            'status' => isset($data['status']) ? (int) $data['status'] : null,
        ];
    }

    /**
     * 檢查會員是否存在
     *
     * @param int $accountId 會員ID
     * @return bool
     */
    public function accountExists(int $accountId): bool
    {
        return Account::query()->where('id', $accountId)->exists();
    }

    /**
     * 取得中心統計資訊
     * 使用快取優化
     *
     * @param int $id 中心ID
     * @return array
     */
    public function getCenterStats(int $id): array
    {
        return $this->cacheService->getCenterStats($id);
    }
}
