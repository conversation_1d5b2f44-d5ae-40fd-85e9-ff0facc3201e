<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Main\Account;
use App\Models\Main\VipTypeRelation;
use Illuminate\Support\Facades\DB;

class UpdateVipType extends Command
{
    /**
     * 指令名稱與描述
     *
     * @var string
     */
    protected $signature = 'vip:update-type';

    protected $description = '將 account.vip_type = 0 且 vip_type_course > 0 的用戶，批次更新 vip_type 並寫入 vip_type_relation';

    /**
     * 執行指令
     *
     * @return int
     */
    public function handle()
    {
        // 主要邏輯
        $accounts = Account::where('vip_type', 0)
            ->where('vip_type_course', '>', 0)
            ->get();

        if ($accounts->isEmpty()) {
            $this->info('無需處理的帳號');
            return 0;
        }

        DB::connection('main_db')->beginTransaction();
        try {
            foreach ($accounts as $account) {
                // 更新 vip_type
                $account->vip_type = $account->vip_type_course;
                $account->save();

                // 新增 vip_type_relation
                VipTypeRelation::updateOrCreate(
                    ['user_id' => $account->id, 'vip_type_id' => $account->vip_type_course],
                    [
                        'datetime' => date('Y-m-d H:i:s'),
                    ]
                );
            }
            DB::connection('main_db')->commit();
            $this->info('批次更新完成，共處理 ' . $accounts->count() . ' 筆');
        } catch (\Exception $e) {
            DB::connection('main_db')->rollBack();
            $this->error('發生錯誤：' . $e->getMessage());
            return 1;
        }
        return 0;
    }
}
