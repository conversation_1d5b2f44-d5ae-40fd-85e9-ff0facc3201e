<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\MessageBag;

trait HandlesValidationErrors
{
    /**
     * 處理驗證錯誤的統一方法
     */
    protected function handleValidationError(
        ValidationException $e,
        string $operation = 'operation',
        bool $isAjax = null
    ): JsonResponse|RedirectResponse {
        $isAjax = $isAjax ?? request()->ajax();

        // 記錄驗證錯誤
        $this->logValidationError($e, $operation);

        if ($isAjax) {
            return $this->jsonValidationErrorResponse($e);
        }

        return $this->redirectValidationErrorResponse($e);
    }

    /**
     * 處理一般錯誤的統一方法
     */
    protected function handleGeneralError(
        \Exception $e,
        string $operation = 'operation',
        string $userMessage = '操作失敗，請稍後再試',
        bool $isAjax = null
    ): JsonResponse|RedirectResponse {
        $isAjax = $isAjax ?? request()->ajax();

        // 記錄系統錯誤
        $this->logSystemError($e, $operation);

        if ($isAjax) {
            return $this->jsonErrorResponse($userMessage, $e);
        }

        return $this->redirectErrorResponse($userMessage);
    }

    /**
     * 處理業務邏輯錯誤
     */
    protected function handleBusinessError(
        string $message,
        array $data = [],
        int $statusCode = 409,
        bool $isAjax = null
    ): JsonResponse|RedirectResponse {
        $isAjax = $isAjax ?? request()->ajax();

        // 記錄業務邏輯錯誤
        Log::info('業務邏輯錯誤', [
            'message' => $message,
            'data' => $data,
            'admin_id' => session('admin_id'),
            'ip' => request()->ip(),
            'url' => request()->fullUrl()
        ]);

        if ($isAjax) {
            return response()->json([
                'status' => false,
                'message' => $message,
                'data' => $data,
                'error_type' => 'business_logic'
            ], $statusCode);
        }

        return redirect()->back()
            ->withErrors(['business' => $message])
            ->withInput();
    }

    /**
     * JSON 驗證錯誤回應
     */
    protected function jsonValidationErrorResponse(ValidationException $e): JsonResponse
    {
        return response()->json([
            'status' => false,
            'message' => '資料驗證失敗',
            'errors' => $e->errors(),
            'error_type' => 'validation',
            'error_count' => count($e->errors()),
            'first_error' => $this->getFirstError($e->errors())
        ], 422);
    }

    /**
     * 重導向驗證錯誤回應
     */
    protected function redirectValidationErrorResponse(ValidationException $e): RedirectResponse
    {
        return redirect()->back()
            ->withErrors($e->errors())
            ->withInput()
            ->with('error_type', 'validation');
    }

    /**
     * JSON 一般錯誤回應
     */
    protected function jsonErrorResponse(string $message, \Exception $e = null): JsonResponse
    {
        $response = [
            'status' => false,
            'message' => $message,
            'error_type' => 'system'
        ];

        // 在開發環境中包含更多錯誤資訊
        if (config('app.debug') && $e) {
            $response['debug'] = [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return response()->json($response, 500);
    }

    /**
     * 重導向一般錯誤回應
     */
    protected function redirectErrorResponse(string $message): RedirectResponse
    {
        return redirect()->back()
            ->with('error', $message)
            ->with('error_type', 'system')
            ->withInput();
    }

    /**
     * 成功回應
     */
    protected function successResponse(
        string $message,
        array $data = [],
        string $redirectUrl = null,
        bool $isAjax = null
    ): JsonResponse|RedirectResponse {
        $isAjax = $isAjax ?? request()->ajax();

        if ($isAjax) {
            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => $data
            ]);
        }

        $redirect = $redirectUrl ? redirect($redirectUrl) : redirect()->back();

        return $redirect->with('success', $message);
    }

    /**
     * 記錄驗證錯誤
     */
    protected function logValidationError(ValidationException $e, string $operation): void
    {
        Log::warning('資料驗證失敗', [
            'operation' => $operation,
            'errors' => $e->errors(),
            'admin_id' => session('admin_id'),
            'admin_name' => session('admin_name'),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'input' => $this->sanitizeInputForLog(request()->all()),
            'timestamp' => now()
        ]);
    }

    /**
     * 記錄系統錯誤
     */
    protected function logSystemError(\Exception $e, string $operation): void
    {
        Log::error('系統錯誤', [
            'operation' => $operation,
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'admin_id' => session('admin_id'),
            'admin_name' => session('admin_name'),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'input' => $this->sanitizeInputForLog(request()->all()),
            'timestamp' => now()
        ]);
    }

    /**
     * 獲取第一個錯誤訊息
     */
    protected function getFirstError(array $errors): string
    {
        foreach ($errors as $field => $messages) {
            if (is_array($messages) && !empty($messages)) {
                return $messages[0];
            }
        }
        return '未知錯誤';
    }

    /**
     * 清理輸入資料用於日誌記錄
     */
    protected function sanitizeInputForLog(array $input): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'secret',
            'key',
            '_token',
            'api_key',
            'access_token'
        ];

        $sanitized = $input;

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '***';
            }
        }

        return $sanitized;
    }

    /**
     * 驗證欄位錯誤格式化
     */
    protected function formatFieldErrors(array $errors): array
    {
        $formatted = [];

        foreach ($errors as $field => $messages) {
            $formatted[] = [
                'field' => $field,
                'message' => is_array($messages) ? $messages[0] : $messages,
                'all_messages' => is_array($messages) ? $messages : [$messages]
            ];
        }

        return $formatted;
    }

    /**
     * 檢查是否為 AJAX 請求
     */
    protected function isAjaxRequest(): bool
    {
        return request()->ajax() ||
            request()->wantsJson() ||
            request()->expectsJson() ||
            request()->header('Content-Type') === 'application/json';
    }

    /**
     * 創建統一的錯誤回應格式
     */
    protected function createErrorResponse(
        string $message,
        array $errors = [],
        array $data = [],
        string $errorType = 'general',
        int $statusCode = 400
    ): array {
        return [
            'status' => false,
            'message' => $message,
            'errors' => $errors,
            'data' => $data,
            'error_type' => $errorType,
            'timestamp' => now()->toISOString(),
            'request_id' => request()->header('X-Request-ID', uniqid())
        ];
    }

    /**
     * 創建統一的成功回應格式
     */
    protected function createSuccessResponse(
        string $message,
        array $data = [],
        array $meta = []
    ): array {
        return [
            'status' => true,
            'message' => $message,
            'data' => $data,
            'meta' => $meta,
            'timestamp' => now()->toISOString(),
            'request_id' => request()->header('X-Request-ID', uniqid())
        ];
    }

    /**
     * 處理資料庫錯誤
     */
    protected function handleDatabaseError(
        \Exception $e,
        string $operation = 'database_operation',
        bool $isAjax = null
    ): JsonResponse|RedirectResponse {
        $isAjax = $isAjax ?? request()->ajax();

        // 根據錯誤類型提供不同的用戶友好訊息
        $userMessage = $this->getDatabaseErrorMessage($e);

        // 記錄詳細的資料庫錯誤
        Log::error('資料庫錯誤', [
            'operation' => $operation,
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'admin_id' => session('admin_id'),
            'ip' => request()->ip(),
            'timestamp' => now()
        ]);

        if ($isAjax) {
            return response()->json([
                'status' => false,
                'message' => $userMessage,
                'error_type' => 'database'
            ], 500);
        }

        return redirect()->back()
            ->with('error', $userMessage)
            ->with('error_type', 'database')
            ->withInput();
    }

    /**
     * 獲取資料庫錯誤的用戶友好訊息
     */
    protected function getDatabaseErrorMessage(\Exception $e): string
    {
        $message = $e->getMessage();
        $code = $e->getCode();

        // 常見的資料庫錯誤處理
        if (strpos($message, 'Duplicate entry') !== false) {
            return '資料重複，請檢查輸入的資訊';
        }

        if (strpos($message, 'foreign key constraint') !== false) {
            return '無法執行操作，因為存在相關聯的資料';
        }

        if (strpos($message, 'Connection refused') !== false) {
            return '資料庫連線失敗，請稍後再試';
        }

        if (strpos($message, 'Timeout') !== false) {
            return '操作超時，請稍後再試';
        }

        // 預設錯誤訊息
        return '資料庫操作失敗，請稍後再試';
    }
}
