/**
 * 中心管理驗證系統樣式
 * 提供統一的錯誤顯示和用戶體驗
 */

/* 表單驗證狀態 */
.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 24 24'%3e%3ccircle cx='12' cy='12' r='10'/%3e%3cline x1='15' y1='9' x2='9' y2='15'/%3e%3cline x1='9' y1='9' x2='15' y2='15'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.88-1.88.94-.94L3.66 0 2.72.94l-1.88 1.88L.94 2.72l.94.94L2.3 6.73z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

/* 錯誤訊息樣式 */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
    animation: fadeInUp 0.3s ease-out;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
    animation: fadeInUp 0.3s ease-out;
}

/* 錯誤圖標 */
.field-error-icon {
    color: #dc3545 !important;
    animation: shake 0.5s ease-in-out;
}

.field-success-icon {
    color: #28a745 !important;
    animation: bounceIn 0.5s ease-out;
}

/* 表單錯誤摘要 */
.form-error-summary {
    margin-bottom: 1rem;
    border-left: 4px solid #dc3545;
    animation: slideInDown 0.3s ease-out;
}

.form-error-summary h6 {
    color: #721c24;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-error-summary ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.form-error-summary li {
    margin-bottom: 0.25rem;
    color: #721c24;
}

.form-error-summary li strong {
    color: #dc3545;
}

/* 成功訊息 */
.form-success-summary {
    margin-bottom: 1rem;
    border-left: 4px solid #28a745;
    animation: slideInDown 0.3s ease-out;
}

/* 載入狀態 */
.input-loading-indicator {
    pointer-events: none;
    z-index: 10;
}

.input-loading-indicator i {
    animation: spin 1s linear infinite;
    color: #6c757d;
}

/* 字元計數器 */
.character-counter {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    text-align: right;
}

.character-counter.text-warning {
    color: #ffc107 !important;
    font-weight: 500;
}

.character-counter.text-danger {
    color: #dc3545 !important;
    font-weight: 600;
    animation: pulse 1s infinite;
}

/* 驗證進度指示器 */
.validation-progress {
    height: 3px;
    background-color: #e9ecef;
    border-radius: 1.5px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.validation-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    border-radius: 1.5px;
    transition: width 0.3s ease;
}

/* 工具提示增強 */
.validation-tooltip {
    position: absolute;
    z-index: 1000;
    background: #343a40;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.validation-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.validation-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #343a40 transparent transparent transparent;
}

/* 表單組增強 */
.form-group.has-validation {
    position: relative;
}

.form-group.has-validation .form-control {
    padding-right: 2.5rem;
}

.form-group.has-validation .validation-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    pointer-events: none;
}

/* 即時驗證狀態 */
.form-control.validating {
    border-color: #6c757d;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%236c757d' viewBox='0 0 16 16'%3e%3cpath d='M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z'/%3e%3cpath d='M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    animation: pulse 1.5s infinite;
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .form-error-summary {
        margin-left: -15px;
        margin-right: -15px;
        border-left: none;
        border-top: 4px solid #dc3545;
        border-radius: 0;
    }

    .invalid-feedback {
        font-size: 0.8rem;
    }

    .character-counter {
        font-size: 0.7rem;
    }

    .validation-tooltip {
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        white-space: normal;
        max-width: 90vw;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    .form-control.is-invalid {
        background-color: #2d3748;
        border-color: #e53e3e;
        color: #f7fafc;
    }

    .form-control.is-valid {
        background-color: #2d3748;
        border-color: #38a169;
        color: #f7fafc;
    }

    .invalid-feedback {
        color: #feb2b2;
    }

    .valid-feedback {
        color: #9ae6b4;
    }

    .form-error-summary {
        background-color: #2d3748;
        border-color: #e53e3e;
        color: #feb2b2;
    }

    .form-error-summary h6 {
        color: #feb2b2;
    }

    .form-error-summary li {
        color: #e2e8f0;
    }

    .form-error-summary li strong {
        color: #feb2b2;
    }
}

/* 高對比度模式 */
@media (prefers-contrast: high) {
    .form-control.is-invalid {
        border-width: 2px;
        border-color: #000;
    }

    .form-control.is-valid {
        border-width: 2px;
        border-color: #000;
    }

    .invalid-feedback {
        color: #000;
        font-weight: bold;
    }

    .valid-feedback {
        color: #000;
        font-weight: bold;
    }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
    .invalid-feedback,
    .valid-feedback,
    .form-error-summary,
    .field-error-icon,
    .field-success-icon,
    .validation-tooltip,
    .form-control.validating {
        animation: none;
        transition: none;
    }

    .validation-progress-bar {
        transition: none;
    }
}

/* UX 增強動畫 */
.shake-animation {
    animation: shake 0.5s ease-in-out;
}

.success-animation {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 列印樣式 */
@media print {
    .form-error-summary,
    .invalid-feedback,
    .valid-feedback {
        color: #000 !important;
        background: transparent !important;
        border: 1px solid #000 !important;
    }

    .field-error-icon,
    .field-success-icon,
    .input-loading-indicator,
    .shake-animation,
    .success-animation {
        display: none !important;
        animation: none !important;
    }
}
