.order_content {
    overflow-y: hidden;
}

.status li {
    width: 18%;
    /* flex-shrink:0; */
    position: relative;
    background-color: #ab8d60;
    z-index: 1;
}

.status li:not(:first-child) {
    margin-left: -1%;
}

.status li:first-child a {
    padding: 5px 20px;
}

.status li::before {
    content: "";
    position: absolute;
    z-index: -1;
    width: 99.5%;
    height: 98.525%;
    background-color: white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.status a {
    display: inline-block;
    padding: 5px 0px 5px 40px;
    color: #ab8d60;
    font-weight: bold;
    width: 100%;
}

.status li:first-child,
.status li:first-child::before {
    clip-path: polygon(0% 0%, 95% 0%, 100% 50%, 95% 100%, 0% 100%);
}

.status li:nth-child(2),
.status li:nth-child(3),
.status li:nth-child(4),
.status li:nth-child(5),
.status li:nth-child(2)::before,
.status li:nth-child(3)::before,
.status li:nth-child(4)::before,
.status li:nth-child(5)::before {
    clip-path: polygon(95% 0, 100% 50%, 95% 100%, 0% 100%, 5% 50%, 0% 0%);
}

.status li:last-child,
.status li:last-child::before {
    clip-path: polygon(0 0, 100% 0%, 100% 100%, 0 100%, 5% 50%);
}

.status li.active a {
    color: #ffffff;
}
.status li.active::before {
    background-color: #ab8d60;
}

.redo-icon,
.page-switch-btn {
    margin: 0 10px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    margin-left: 5px;
    margin-right: 5px;
}

.redo-icon i,
.page-switch-btn i {
    font-size: 20px;
    color: #8f8f8f;
}

.frame .edit {
    padding: 4px 18px;
}

.frame .edit i {
    font-size: 14px;
    color: #8f8f8f;
}

.edit_form th a {
    color: #ffffff;
}

.edit_form table td a {
    color: rgb(33, 37, 41);
    text-decoration: underline;
}

.edit_form td .product {
    text-decoration: underline;
}

.tool_item .edit-item {
    top: 34px;
    padding-bottom: 10%;
}

.edit-item a:hover {
    color: #ab8d60 !important;
}

.tool_item .sendbtn,
.distributor-btn,
.content .export-btn,
.content .sum-btn,
.frame .print-btn {
    background-color: #ebebeb;
    color: #757575 !important;
    padding: 4px 18px;
}

.distributor-btn.active,
.tool_item .sendbtn:hover,
.distributor-btn:hover,
.content .export-btn:hover,
.content .sum-btn:hover,
.frame .print-btn:hover {
    background-color: #ab8d60;
    color: #ffffff !important;
}

.tool_item .activeSendbtn {
    background-color: #ab8d60;
    color: #ffffff !important;
}

.btn:focus, .tool_item .sendbtn:focus,
.frame .print-btn:focus,
.distributor-btn:focus {
    box-shadow: none;
}

.tool_item .removebtn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    background-color: #ab8d60;
    margin-left: 8px;
}

.modalSelect .modal-header {
    background-color: #000000;
    color: #ffffff;
    justify-content: space-between;
    align-items: center;
}


.modalSelect input:hover, .modalSelect input:focus, .modalSelect select:hover, .modalSelect select:focus{
    border-color: #000000;
}


.modalSelect .mx-datepicker-range {
    width: 280px;
}

/* .modal-body .mx-input:hover,
.mx-input:focus {
    border-color: #000000;
} */


.modalSelect button.close {
    background-color: #ffffff;
    color: #000000;
    padding: 4px;
    border-radius: 50%;
    opacity: 1;
    margin: 0;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
}

.modalSelect button.close:focus {
    outline: none;
}

.modalSelect .search-items {
    display: block;
}

.modalSelect select {
    width: 201px;
    color: #757575;
}

.modalSelect .modal-footer {
    border-top: none;
    justify-content: space-between;
    padding-top: 0;
}

.modalSelect .modal-footer button {
    width: 49%;
}

.modalSelect .modal-footer button:hover {
    opacity: 0.8;
}

.modalSelect .modal-footer button:focus {
    box-shadow: none;
}

.modalSelect .modal-footer .clearbtn {
    border: 1px solid #000000;
    color: #757575 !important;
    background-color: transparent;
}
.modalSelect .modal-footer .searchbtn {
    color: #757575 !important;
}

.content .export-form {
    width: auto;
    display: flex;
}

.content .export-form li {
    flex: initial;
}

.content .export-btn:focus,
.content .sum-btn:focus {
    box-shadow: none;
    border: none;
}

.content .export-btn:hover,
.content .sum-btn:hover {
    background-color: #ab8d60;
    color: #ffffff !important;
}

th .not-paid-num {
    display: inline-block;
    color: #ffffff;
    background-color: #ab8d60;
    font-size: 8px;
    padding: 3px 6px;
    border-radius: 15px;
}

.edit_form th,
.edit_form td {
    vertical-align: middle !important;
    text-align: center;
}

.edit_form table tbody tr:nth-of-type(even) {
    background-color: #f6f6f6;
}

/* .title {
    margin: 0 auto;
} */

.title h6 {
    color: #000000;
    font-size: 36px;
    font-weight: bold;
}

.title .switch-tabs a {
    color: #757575;
    background-color: #ebebeb;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 5px;
}

.title .switch-tabs a.active {
    color: #ffffff;
    background-color: #007bff;
}

.final-check {
    padding: 8px 10px;
    border: 1px solid #c6c6c6;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 25%);
    border-radius: 5px;
}

.final-check a {
    color: #ffffff;
    background-color: #ab8d60;
    padding: 20px 28px;
    border-radius: 4px;
    font-size: 20px;
    font-weight: bold;
}

.final-check a:hover,
.final-check a:hover:focus {
    color: #ab8d60;
    background-color: transparent;
    outline: 1px solid #ab8d60;
}

.edit_form .confirm-btn {
    color: #ffffff;
    background-color: #ab8d60;
    border-radius: 5px;
    padding: 5px 8px;
    border: none;
    cursor: pointer;
}

.edit_form .confirm-btn:focus, .edit_form .confirm-btn:hover {
    color: #ab8d60;
    background-color: transparent;
    outline: 1px solid #ab8d60;
}

.edit_form .pick-num:focus {
    box-shadow: 0 0 0 0.2rem rgba(171, 140, 95, 0.25);
    border-color: #ab8d60;
}

.edit_form .more-btn {
    background-color: #ab8d60;
    color: #ffffff;
    border-radius: 20px;
    padding: 1px 5px;
    text-decoration: none;
}

.edit_form .more-btn:hover {
    outline: 1px solid #ab8d60;
    color: #ab8d60;
    background-color: transparent;
}

.edit_form .finish-btn{
    padding:5px 8px; 
    color:#ffffff; 
    background-color:#ab8d60; 
    text-decoration:none;
    border-radius:5px;
    border:none;
    cursor:pointer;
}

.edit_form .finish-btn:hover, .edit_form .finish-btn:focus{
    outline: 1px solid #ab8d60;
    color: #ab8d60;
    background-color: transparent;
}

#productDetail .close {
    width: 24px;
    height: 24px;
    padding: 4px;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color:#ACACAC;
    color: white;
    border-radius: 50%;  
    font-size:1.2rem;
}
/* #productDetail .close span{
    font-size:16px;
} */

#productDetail .close:focus {
    outline: none;
}

#productDetail .modal-title span:last-child {
    text-decoration: underline;
}

#productDetail .modal-header {
    border-bottom: none;
    padding-left: 40px;
    padding-right: 40px;
}

#productDetail .modal-body {
    padding-left: 40px;
    padding-right: 40px;
}

#productDetail .modal-footer {
    border-top: none;
    padding-left: 40px;
    padding-right: 40px;
}

#productDetail .detail > div span:first-child {
    width: 85%;
}

#productDetail .detail > div {
    padding: 24px 4%;
}

#productDetail .detail > div:nth-child(odd) {
    background-color: #f4f4f4;
}

#productDetail .detail > div:first-child {
    background-color: #4d4d4d;
    padding: 12px 4%;
    color: #ffffff;
}

#productDetail .detail > div:last-child {
    border-bottom: 1px solid #d9d9d9;
}

#productDetail .message {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    min-height: 100px;
    padding: 5px;
}

#productDetail .total {
    padding: 24px 4%;
}

#productDetail .total span:first-child {
    width: 85%;
}

#productDetail .modal-footer button {
    color: #757575;
    background-color: #e7e7e7;
    padding: 10px 40px;
    border: none;
}

#productDetail .modal-footer button:hover {
    border: initial;
}



.edit_form .transport-btn {
    padding: 5px 8px;
    color: #ffffff;
    font-size: 14px;
    border: none;
    border-radius: 5px;
    background-color: #bcbcbc;
    cursor: pointer;
}

.edit_form .transport-btn.already-print {
    background-color: #5fa546;
}

.modalHint .modal-content {
    box-shadow: 0 0 4px 0 rgba(255, 255, 255, 60%);
}

.modalHint .modal-header {
    border-bottom: none;
}

.modalHint .close {
    padding: 0;
    margin: 0;
}

.modalHint .modal-body {
    color: #d05050;
    padding-left: 8%;
    padding-right: 8%;
}

.modalHint .modal-footer {
    padding-left: 8%;
    padding-right: 8%;
    border-top: none;
}

.modalHint .modal-footer .btn {
    width: 50%;
}

.modalHint .modal-footer .btn:first-child {
    color: #757575;
    background-color: #d9d9d9;
}

.modalHint .modal-footer .btn:last-child {
    color: #ffffff;
    background-color: #ab8d60;
}

.modalHint#hint2 .modal-body {
    color: #525252;
}

.modalHint#hint2 .modal-footer .btn {
    width: 100%;
}
