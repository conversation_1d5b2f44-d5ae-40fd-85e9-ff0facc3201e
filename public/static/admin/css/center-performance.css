/**
 * Center Management Performance Optimizations CSS
 * 中心管理效能優化樣式
 */

/* 載入狀態優化 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 骨架屏載入效果 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 16px;
    margin: 8px 0;
    border-radius: 4px;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

/* 虛擬滾動優化 */
.virtual-scroll-container {
    position: relative;
    overflow: auto;
    height: 400px;
}

.virtual-scroll-content {
    position: relative;
}

.virtual-scroll-item {
    position: absolute;
    left: 0;
    right: 0;
    transition: transform 0.1s ease;
}

/* 延遲載入優化 */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* 自動完成優化 */
.ui-autocomplete {
    max-height: 200px;
    overflow-y: auto;
    z-index: 1050;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ui-autocomplete .ui-menu-item {
    padding: 0;
    margin: 0;
}

.ui-autocomplete .ui-menu-item-wrapper {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.ui-autocomplete .ui-menu-item-wrapper:hover,
.ui-autocomplete .ui-menu-item-wrapper.ui-state-active {
    background-color: #f8f9fa;
}

.account-item {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.account-item strong {
    color: #333;
    margin-right: 8px;
}

.account-item .text-muted {
    color: #6c757d;
    font-size: 12px;
}

/* 表格優化 */
.optimized-table {
    table-layout: fixed;
    width: 100%;
}

.optimized-table th,
.optimized-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 響應式優化 */
@media (max-width: 768px) {
    .loading-overlay {
        background: rgba(255, 255, 255, 0.9);
    }

    .loading-spinner {
        width: 30px;
        height: 30px;
        border-width: 3px;
    }

    .ui-autocomplete {
        max-height: 150px;
        font-size: 14px;
    }

    .account-item {
        font-size: 13px;
    }
}

/* 效能指標顯示（開發模式） */
.performance-metrics {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
    z-index: 9999;
    display: none;
}

.performance-metrics.show {
    display: block;
}

.performance-metrics .metric {
    margin: 2px 0;
}

.performance-metrics .metric-label {
    color: #ccc;
}

.performance-metrics .metric-value {
    color: #0f0;
    font-weight: bold;
}

/* 快取狀態指示器 */
.cache-indicator {
    position: relative;
}

.cache-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cache-indicator.cached::after {
    opacity: 1;
}

/* 預載入狀態 */
.preloading {
    position: relative;
}

.preloading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #007bff, transparent);
    animation: preload 2s infinite;
}

@keyframes preload {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 錯誤狀態優化 */
.error-state {
    padding: 20px;
    text-align: center;
    color: #dc3545;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin: 10px 0;
}

.error-state .error-icon {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.error-state .error-message {
    font-size: 16px;
    margin-bottom: 10px;
}

.error-state .error-actions {
    margin-top: 15px;
}

/* 空狀態優化 */
.empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 20px 0;
}

.empty-state .empty-icon {
    font-size: 64px;
    margin-bottom: 15px;
    opacity: 0.3;
}

.empty-state .empty-message {
    font-size: 18px;
    margin-bottom: 10px;
}

.empty-state .empty-description {
    font-size: 14px;
    margin-bottom: 20px;
}

/* 動畫優化 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* 減少重繪和回流 */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
