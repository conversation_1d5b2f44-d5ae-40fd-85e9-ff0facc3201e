/**
 * 中心管理前端用戶體驗增強樣式
 * 提供載入狀態、通知系統、進度指示器等視覺效果
 */

/* ===== 載入狀態指示器 ===== */

/* 全域載入遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 500;
}

/* 按鈕載入狀態 */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading .spinner-icon {
    animation: spin 1s linear infinite;
}

/* 表單載入狀態 */
.form-loading {
    position: relative;
    opacity: 0.7;
}

.form-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    z-index: 10;
    animation: fadeInDown 0.3s ease-out;
}

/* AJAX 載入指示器 */
.ajax-loading-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    z-index: 9998;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 元素載入遮罩 */
.element-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
    border-radius: inherit;
}

/* ===== 通知系統 ===== */

/* 通知容器 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9997;
    max-width: 400px;
    width: 100%;
}

.notification-stack {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 通知基本樣式 */
.notification {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    overflow: hidden;
    position: relative;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-exit {
    transform: translateX(100%);
    opacity: 0;
}

/* 通知類型樣式 */
.notification-success {
    border-left-color: #28a745;
}

.notification-error {
    border-left-color: #dc3545;
}

.notification-warning {
    border-left-color: #ffc107;
}

.notification-info {
    border-left-color: #17a2b8;
}

/* 通知內容 */
.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    gap: 0.75rem;
}

.notification-icon {
    flex-shrink: 0;
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.notification-body {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: #212529;
}

.notification-message {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

.notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem;
    line-height: 1;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: #495057;
}

/* 通知進度條 */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
}

.notification-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
    animation: notificationProgress linear forwards;
}

@keyframes notificationProgress {
    from { width: 100%; }
    to { width: 0%; }
}

/* ===== 表單進度指示器 ===== */

.form-progress {
    margin-bottom: 1rem;
}

.form-progress .progress {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.form-progress .progress-bar {
    transition: width 0.3s ease;
    border-radius: 2px;
}

.form-progress-text {
    font-size: 0.75rem;
    font-weight: 500;
}

/* ===== 鍵盤快捷鍵說明 ===== */

.keyboard-shortcuts-help {
    max-width: 300px;
}

.keyboard-shortcuts-help h5 {
    margin-bottom: 1rem;
    color: #495057;
}

.keyboard-shortcuts-help ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.keyboard-shortcuts-help li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.875rem;
}

.keyboard-shortcuts-help li:last-child {
    border-bottom: none;
}

.keyboard-shortcuts-help kbd {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* ===== 表單互動增強 ===== */

/* 未儲存變更警告 */
#unsaved-changes-warning {
    position: sticky;
    top: 0;
    z-index: 100;
    margin-bottom: 1rem;
    animation: slideInDown 0.3s ease-out;
}

/* 表單變更指示器 */
.form-changed {
    position: relative;
}

.form-changed::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #ffc107;
    border-radius: 0.375rem;
    pointer-events: none;
    animation: pulse 2s infinite;
}

/* 智能焦點樣式 */
.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* 表單欄位增強 */
.form-group.enhanced {
    position: relative;
}

.form-group.enhanced .form-control {
    padding-right: 2.5rem;
}

.form-group.enhanced .field-status-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-group.enhanced .form-control:focus + .field-status-icon,
.form-group.enhanced .form-control.is-valid + .field-status-icon,
.form-group.enhanced .form-control.is-invalid + .field-status-icon {
    opacity: 1;
}

/* ===== 動畫效果 ===== */

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== 響應式設計 ===== */

@media (max-width: 768px) {
    .notification-container {
        left: 10px;
        right: 10px;
        top: 10px;
        max-width: none;
    }

    .notification {
        margin-bottom: 10px;
    }

    .loading-overlay .loading-spinner {
        padding: 0 1rem;
    }

    .loading-text {
        font-size: 1rem;
    }

    .keyboard-shortcuts-help {
        max-width: none;
    }

    .ajax-loading-indicator {
        left: 10px;
        right: 10px;
        transform: none;
    }
}

@media (max-width: 576px) {
    .notification-content {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .notification-title {
        font-size: 0.875rem;
    }

    .notification-message {
        font-size: 0.8rem;
    }

    .form-progress-text {
        font-size: 0.7rem;
    }
}

/* ===== 深色模式支援 ===== */

@media (prefers-color-scheme: dark) {
    .loading-overlay {
        background: rgba(0, 0, 0, 0.7);
    }

    .notification {
        background: #343a40;
        color: #f8f9fa;
    }

    .notification-title {
        color: #f8f9fa;
    }

    .notification-message {
        color: #adb5bd;
    }

    .notification-close {
        color: #adb5bd;
    }

    .notification-close:hover {
        color: #f8f9fa;
    }

    .form-loading-indicator {
        background: rgba(52, 58, 64, 0.95);
        border-color: #495057;
        color: #f8f9fa;
    }

    .ajax-loading-indicator {
        background: rgba(248, 249, 250, 0.9);
        color: #212529;
    }

    .keyboard-shortcuts-help {
        color: #f8f9fa;
    }

    .keyboard-shortcuts-help h5 {
        color: #f8f9fa;
    }

    .keyboard-shortcuts-help kbd {
        background-color: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }

    #unsaved-changes-warning {
        background-color: #664d03;
        border-color: #997404;
        color: #ffecb5;
    }
}

/* ===== 高對比度模式 ===== */

@media (prefers-contrast: high) {
    .notification {
        border-width: 2px;
        border-style: solid;
    }

    .notification-success {
        border-color: #000;
        background: #d4edda;
    }

    .notification-error {
        border-color: #000;
        background: #f8d7da;
    }

    .notification-warning {
        border-color: #000;
        background: #fff3cd;
    }

    .notification-info {
        border-color: #000;
        background: #d1ecf1;
    }

    .form-control:focus {
        border-color: #000;
        box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.25);
    }
}

/* ===== 減少動畫偏好 ===== */

@media (prefers-reduced-motion: reduce) {
    .notification,
    .form-loading-indicator,
    .ajax-loading-indicator,
    .element-loading-overlay,
    .form-progress .progress-bar,
    .field-status-icon,
    .form-control,
    .form-select,
    #unsaved-changes-warning {
        animation: none;
        transition: none;
    }

    .notification-progress-bar {
        animation: none;
    }

    .btn-loading .spinner-icon {
        animation: none;
    }

    .loading-spinner .spinner-border {
        animation: none;
    }
}

/* ===== 列印樣式 ===== */

@media print {
    .loading-overlay,
    .notification-container,
    .ajax-loading-indicator,
    .form-loading-indicator,
    .element-loading-overlay,
    #unsaved-changes-warning {
        display: none !important;
    }

    .form-progress {
        display: none !important;
    }

    .notification {
        display: none !important;
    }
}

/* ===== 輔助功能增強 ===== */

/* 螢幕閱讀器專用內容 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦點指示器增強 */
.notification-close:focus,
button:focus,
.btn:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* 高對比度焦點指示器 */
@media (prefers-contrast: high) {
    .notification-close:focus,
    button:focus,
    .btn:focus {
        outline: 3px solid #000;
        outline-offset: 2px;
    }
}

/* 觸控裝置優化 */
@media (hover: none) and (pointer: coarse) {
    .notification-close {
        padding: 0.5rem;
        font-size: 1.5rem;
    }

    .btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* ===== 自定義屬性（CSS 變數）===== */

:root {
    --notification-border-radius: 0.5rem;
    --notification-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --notification-padding: 1rem;
    --notification-gap: 0.75rem;
    --loading-overlay-bg: rgba(0, 0, 0, 0.5);
    --loading-spinner-size: 3rem;
    --progress-bar-height: 4px;
    --animation-duration: 0.3s;
    --animation-timing: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 深色模式變數 */
@media (prefers-color-scheme: dark) {
    :root {
        --loading-overlay-bg: rgba(0, 0, 0, 0.7);
        --notification-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
}
