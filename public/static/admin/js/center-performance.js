/**
 * Center Management Performance Optimizations
 * 中心管理效能優化腳本
 */

(function ($) {
    "use strict";

    // 效能優化配置
    const PERFORMANCE_CONFIG = {
        // 防抖延遲時間
        DEBOUNCE_DELAY: 300,
        // 快取時間（毫秒）
        CACHE_TTL: 300000, // 5分鐘
        // 分頁大小
        PAGE_SIZE: 15,
        // 最大搜尋結果
        MAX_SEARCH_RESULTS: 50,
    };

    // 本地快取管理
    const CacheManager = {
        cache: new Map(),

        set: function (key, data, ttl = PERFORMANCE_CONFIG.CACHE_TTL) {
            const expiry = Date.now() + ttl;
            this.cache.set(key, { data, expiry });
        },

        get: function (key) {
            const item = this.cache.get(key);
            if (!item) return null;

            if (Date.now() > item.expiry) {
                this.cache.delete(key);
                return null;
            }

            return item.data;
        },

        clear: function (pattern) {
            if (pattern) {
                for (let key of this.cache.keys()) {
                    if (key.includes(pattern)) {
                        this.cache.delete(key);
                    }
                }
            } else {
                this.cache.clear();
            }
        },
    };

    // 防抖函數
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 節流函數
    function throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => (inThrottle = false), limit);
            }
        };
    }

    // 優化的 AJAX 請求
    const OptimizedAjax = {
        // 請求快取
        requestCache: new Map(),

        // 進行中的請求
        pendingRequests: new Map(),

        request: function (options) {
            const cacheKey = this.getCacheKey(options);

            // 檢查快取
            if (options.cache !== false) {
                const cached = CacheManager.get(cacheKey);
                if (cached) {
                    return Promise.resolve(cached);
                }
            }

            // 檢查是否有相同請求正在進行
            if (this.pendingRequests.has(cacheKey)) {
                return this.pendingRequests.get(cacheKey);
            }

            // 建立新請求
            const request = $.ajax({
                ...options,
                timeout: options.timeout || 10000,
                beforeSend: function (xhr) {
                    // 添加請求標識
                    xhr.setRequestHeader("X-Request-ID", Date.now().toString());
                    if (options.beforeSend) {
                        options.beforeSend(xhr);
                    }
                },
            })
                .done((data) => {
                    // 快取成功的回應
                    if (options.cache !== false && data.status) {
                        CacheManager.set(cacheKey, data);
                    }
                })
                .always(() => {
                    // 清除進行中的請求記錄
                    this.pendingRequests.delete(cacheKey);
                });

            // 記錄進行中的請求
            this.pendingRequests.set(cacheKey, request);

            return request;
        },

        getCacheKey: function (options) {
            return `${options.method || "GET"}_${options.url}_${JSON.stringify(
                options.data || {}
            )}`;
        },
    };

    // 優化的會員搜尋
    const OptimizedAccountSearch = {
        init: function () {
            this.setupAutoComplete();
        },

        setupAutoComplete: function () {
            const self = this;

            $(".account-autocomplete").each(function () {
                const $input = $(this);
                const minLength = parseInt($input.data("min-length")) || 2;

                $input.autocomplete({
                    source: debounce(function (request, response) {
                        self.searchAccounts(request.term, response);
                    }, PERFORMANCE_CONFIG.DEBOUNCE_DELAY),
                    minLength: minLength,
                    delay: 0, // 我們使用自己的防抖
                    select: function (event, ui) {
                        self.handleAccountSelect($input, ui.item);
                    },
                    // 優化渲染
                    create: function () {
                        $(this).data("ui-autocomplete")._renderItem = function (
                            ul,
                            item
                        ) {
                            return $("<li>")
                                .append(
                                    `<div class="account-item">
                                    <strong>${item.number}</strong>
                                    ${
                                        item.name
                                            ? `<span class="text-muted"> - ${item.name}</span>`
                                            : ""
                                    }
                                </div>`
                                )
                                .appendTo(ul);
                        };
                    },
                });
            });
        },

        searchAccounts: function (term, callback) {
            if (term.length < 2) {
                callback([]);
                return;
            }

            OptimizedAjax.request({
                url: "/admin/center/account-suggestions",
                method: "GET",
                data: {
                    query: term,
                    limit: PERFORMANCE_CONFIG.MAX_SEARCH_RESULTS,
                },
                cache: true,
            })
                .done(function (response) {
                    if (response.status && response.data) {
                        callback(response.data.slice(0, 10)); // 限制顯示數量
                    } else {
                        callback([]);
                    }
                })
                .fail(function () {
                    callback([]);
                });
        },

        handleAccountSelect: function ($input, item) {
            $input.val(item.number);
            $input.data("selected-id", item.id);
            $input.trigger("account:selected", [item]);
        },
    };

    // 優化的表格載入
    const OptimizedTable = {
        init: function () {
            this.setupLazyLoading();
            this.setupVirtualScrolling();
        },

        setupLazyLoading: function () {
            // 延遲載入非關鍵資料
            $(".lazy-load").each(function () {
                const $element = $(this);
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            $element.trigger("lazy:load");
                            observer.unobserve(entry.target);
                        }
                    });
                });
                observer.observe(this);
            });
        },

        setupVirtualScrolling: function () {
            // 虛擬滾動（適用於大量資料）
            const $table = $(".virtual-scroll-table");
            if ($table.length === 0) return;

            const rowHeight = 50; // 假設每行高度
            const containerHeight = $table.height();
            const visibleRows = Math.ceil(containerHeight / rowHeight) + 2; // 緩衝區

            let startIndex = 0;

            $table.on(
                "scroll",
                throttle(function () {
                    const scrollTop = $(this).scrollTop();
                    const newStartIndex = Math.floor(scrollTop / rowHeight);

                    if (newStartIndex !== startIndex) {
                        startIndex = newStartIndex;
                        // 更新可見行
                        // 這裡需要根據實際需求實作
                    }
                }, 16)
            ); // 60fps
        },
    };

    // 資源預載
    const ResourcePreloader = {
        init: function () {
            this.preloadCriticalData();
            this.setupPrefetch();
        },

        preloadCriticalData: function () {
            // 預載中心等級和角色資料
            const criticalEndpoints = [
                "/admin/center/center-levels",
                "/admin/center/center-roles",
            ];

            criticalEndpoints.forEach((endpoint) => {
                OptimizedAjax.request({
                    url: endpoint,
                    method: "GET",
                    cache: true,
                });
            });
        },

        setupPrefetch: function () {
            // 滑鼠懸停時預載資料
            $('a[href*="/admin/center/"]').on(
                "mouseenter",
                debounce(function () {
                    const href = $(this).attr("href");
                    if (href && !CacheManager.get(href)) {
                        // 預載頁面資料
                        OptimizedAjax.request({
                            url: href,
                            method: "GET",
                            cache: true,
                        });
                    }
                }, 100)
            );
        },
    };

    // 效能監控
    const PerformanceMonitor = {
        init: function () {
            this.setupMetrics();
            this.setupReporting();
        },

        setupMetrics: function () {
            // 監控關鍵指標
            this.metrics = {
                pageLoadTime: 0,
                ajaxRequestCount: 0,
                cacheHitRate: 0,
                errorCount: 0,
            };

            // 頁面載入時間
            $(window).on("load", () => {
                this.metrics.pageLoadTime = performance.now();
            });

            // AJAX 請求計數
            $(document).ajaxSend(() => {
                this.metrics.ajaxRequestCount++;
            });

            // 錯誤計數
            $(document).ajaxError(() => {
                this.metrics.errorCount++;
            });
        },

        setupReporting: function () {
            // 定期報告效能指標
            setInterval(() => {
                this.reportMetrics();
            }, 60000); // 每分鐘報告一次
        },

        reportMetrics: function () {
            // 計算快取命中率
            const totalRequests =
                OptimizedAjax.requestCache.size + this.metrics.ajaxRequestCount;
            this.metrics.cacheHitRate =
                totalRequests > 0
                    ? (OptimizedAjax.requestCache.size / totalRequests) * 100
                    : 0;

            // 發送到後端（可選）
            if (window.DEBUG_MODE) {
                console.log("Performance Metrics:", this.metrics);
            }
        },
    };

    // 初始化所有優化功能
    $(document).ready(function () {
        // 檢查是否在中心管理頁面
        if (!$("body").hasClass("center-management")) return;

        try {
            OptimizedAccountSearch.init();
            OptimizedTable.init();
            ResourcePreloader.init();
            PerformanceMonitor.init();

            console.log("Center management performance optimizations loaded");
        } catch (error) {
            console.error(
                "Error initializing performance optimizations:",
                error
            );
        }
    });

    // 清理快取（頁面卸載時）
    $(window).on("beforeunload", function () {
        CacheManager.clear();
    });

    // 暴露給全域使用
    window.CenterPerformance = {
        CacheManager,
        OptimizedAjax,
        debounce,
        throttle,
    };
})(jQuery);
