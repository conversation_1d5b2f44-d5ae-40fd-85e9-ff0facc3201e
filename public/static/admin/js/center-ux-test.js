/**
 * 中心管理 UX 增強功能測試腳本
 * 用於驗證所有 UX 增強功能是否正常運作
 */

class CenterUXTest {
    constructor() {
        this.testResults = [];
        this.init();
    }

    init() {
        console.log("🚀 開始中心管理 UX 增強功能測試...");
        this.runAllTests();
    }

    /**
     * 執行所有測試
     */
    async runAllTests() {
        const tests = [
            { name: "載入狀態指示器", method: "testLoadingIndicators" },
            { name: "通知系統", method: "testNotificationSystem" },
            { name: "鍵盤快捷鍵", method: "testKeyboardShortcuts" },
            { name: "表單互動增強", method: "testFormInteractions" },
            { name: "進度指示器", method: "testProgressIndicators" },
            { name: "驗證系統整合", method: "testValidationIntegration" },
        ];

        for (const test of tests) {
            try {
                console.log(`📋 測試：${test.name}`);
                await this[test.method]();
                this.testResults.push({
                    name: test.name,
                    status: "PASS",
                    error: null,
                });
                console.log(`✅ ${test.name} - 通過`);
            } catch (error) {
                this.testResults.push({
                    name: test.name,
                    status: "FAIL",
                    error: error.message,
                });
                console.error(`❌ ${test.name} - 失敗:`, error.message);
            }
        }

        this.showTestResults();
    }

    /**
     * 測試載入狀態指示器
     */
    async testLoadingIndicators() {
        // 測試全域載入遮罩
        if (!window.centerUX) {
            throw new Error("centerUX 實例不存在");
        }

        // 測試顯示全域載入
        window.centerUX.showGlobalLoading("測試載入中...");
        await this.delay(1000);

        if ($("#global-loading-overlay").is(":visible")) {
            console.log("  ✓ 全域載入遮罩顯示正常");
        } else {
            throw new Error("全域載入遮罩未顯示");
        }

        // 測試隱藏全域載入
        window.centerUX.hideGlobalLoading();
        await this.delay(500);

        if (!$("#global-loading-overlay").is(":visible")) {
            console.log("  ✓ 全域載入遮罩隱藏正常");
        } else {
            throw new Error("全域載入遮罩未隱藏");
        }

        // 測試按鈕載入狀態
        const $testBtn = $(
            '<button class="btn btn-primary" id="test-btn">測試按鈕</button>'
        );
        $("body").append($testBtn);

        window.centerUX.setButtonLoading($testBtn, true, "測試載入中...");

        if (
            $testBtn.prop("disabled") &&
            $testBtn.html().includes("測試載入中...")
        ) {
            console.log("  ✓ 按鈕載入狀態設置正常");
        } else {
            throw new Error("按鈕載入狀態設置失敗");
        }

        window.centerUX.setButtonLoading($testBtn, false);
        $testBtn.remove();

        console.log("  ✓ 按鈕載入狀態恢復正常");
    }

    /**
     * 測試通知系統
     */
    async testNotificationSystem() {
        // 測試各種類型的通知
        const notificationTypes = ["success", "error", "warning", "info"];

        for (const type of notificationTypes) {
            const notificationId = window.centerUX.showNotification(
                type,
                `測試${type}通知`,
                `這是一個${type}類型的測試通知`,
                { duration: 1000 }
            );

            await this.delay(100);

            if ($(`#${notificationId}`).length > 0) {
                console.log(`  ✓ ${type} 通知顯示正常`);
            } else {
                throw new Error(`${type} 通知顯示失敗`);
            }
        }

        // 等待通知自動消失
        await this.delay(1500);

        if ($(".notification").length === 0) {
            console.log("  ✓ 通知自動移除正常");
        } else {
            console.log("  ⚠️ 部分通知未自動移除（可能是正常的）");
        }

        // 測試通知聲音（如果支援）
        try {
            window.centerUX.showSuccess("聲音測試", "測試通知聲音", {
                sound: true,
                duration: 1000,
            });
            console.log("  ✓ 通知聲音功能可用");
        } catch (error) {
            console.log("  ⚠️ 通知聲音功能不可用（瀏覽器限制）");
        }
    }

    /**
     * 測試鍵盤快捷鍵
     */
    async testKeyboardShortcuts() {
        // 檢查快捷鍵是否已註冊
        if (
            !window.centerUX.keyboardShortcuts ||
            window.centerUX.keyboardShortcuts.size === 0
        ) {
            throw new Error("鍵盤快捷鍵未註冊");
        }

        console.log(
            `  ✓ 已註冊 ${window.centerUX.keyboardShortcuts.size} 個快捷鍵`
        );

        // 測試快捷鍵組合解析
        const testEvent = {
            ctrlKey: true,
            keyCode: 83, // S
            preventDefault: () => {},
            target: document.body,
        };

        const combination = window.centerUX.getKeyCombination(testEvent);
        if (combination === "ctrl+s") {
            console.log("  ✓ 快捷鍵組合解析正常");
        } else {
            throw new Error(
                `快捷鍵組合解析錯誤，期望 'ctrl+s'，實際 '${combination}'`
            );
        }

        // 測試快捷鍵說明
        window.centerUX.showKeyboardShortcutsHelp();
        await this.delay(500);

        if ($(".keyboard-shortcuts-help").length > 0) {
            console.log("  ✓ 快捷鍵說明顯示正常");
        } else {
            throw new Error("快捷鍵說明顯示失敗");
        }
    }

    /**
     * 測試表單互動增強
     */
    async testFormInteractions() {
        // 創建測試表單
        const testFormHtml = `
            <form id="test-form" data-track-changes="true" data-show-progress="true">
                <input type="text" name="test1" required>
                <input type="text" name="test2" required>
                <input type="text" name="test3">
                <button type="submit">提交</button>
            </form>
        `;

        const $testForm = $(testFormHtml);
        $("body").append($testForm);

        // 測試表單變更追蹤
        $testForm.data("initial-state", $testForm.serialize());
        $testForm.find('input[name="test1"]').val("測試值").trigger("change");

        await this.delay(100);

        if ($testForm.hasClass("form-changed")) {
            console.log("  ✓ 表單變更追蹤正常");
        } else {
            throw new Error("表單變更追蹤失敗");
        }

        // 測試表單進度
        window.centerUX.updateFormProgress($testForm);

        if ($testForm.find(".form-progress").length > 0) {
            console.log("  ✓ 表單進度指示器正常");
        } else {
            throw new Error("表單進度指示器失敗");
        }

        // 清理測試表單
        $testForm.remove();
    }

    /**
     * 測試進度指示器
     */
    async testProgressIndicators() {
        // 創建測試表單
        const $testForm = $(`
            <form id="progress-test-form" data-show-progress="true">
                <input type="text" name="field1" required>
                <input type="text" name="field2" required>
                <input type="text" name="field3" required>
            </form>
        `);

        $("body").append($testForm);

        // 測試進度更新
        window.centerUX.updateFormProgress($testForm);

        if ($testForm.find(".form-progress-bar").length > 0) {
            console.log("  ✓ 進度條創建正常");
        } else {
            throw new Error("進度條創建失敗");
        }

        // 模擬填寫表單
        $testForm.find('input[name="field1"]').val("測試1");
        window.centerUX.updateFormProgress($testForm);

        const progress1 = parseFloat(
            $testForm.find(".form-progress-bar").css("width")
        );

        $testForm.find('input[name="field2"]').val("測試2");
        window.centerUX.updateFormProgress($testForm);

        const progress2 = parseFloat(
            $testForm.find(".form-progress-bar").css("width")
        );

        if (progress2 > progress1) {
            console.log("  ✓ 進度條更新正常");
        } else {
            throw new Error("進度條更新失敗");
        }

        $testForm.remove();
    }

    /**
     * 測試驗證系統整合
     */
    async testValidationIntegration() {
        if (!window.centerValidation) {
            throw new Error("centerValidation 實例不存在");
        }

        // 測試驗證增強功能是否已整合
        const originalShowFieldError = window.centerValidation.showFieldError;
        const originalClearFieldError = window.centerValidation.clearFieldError;

        if (originalShowFieldError && originalClearFieldError) {
            console.log("  ✓ 驗證系統基礎功能正常");
        } else {
            throw new Error("驗證系統基礎功能缺失");
        }

        // 創建測試輸入欄位
        const $testInput = $(
            '<input type="text" id="validation-test" data-validate="centerName">'
        );
        $("body").append($testInput);

        // 測試錯誤顯示增強
        window.centerValidation.showFieldError($testInput, "測試錯誤訊息");

        if ($testInput.hasClass("is-invalid")) {
            console.log("  ✓ 驗證錯誤顯示增強正常");
        } else {
            throw new Error("驗證錯誤顯示增強失敗");
        }

        // 測試錯誤清除增強
        $testInput.val("有效值");
        window.centerValidation.clearFieldError($testInput);

        if (!$testInput.hasClass("is-invalid")) {
            console.log("  ✓ 驗證錯誤清除增強正常");
        } else {
            throw new Error("驗證錯誤清除增強失敗");
        }

        $testInput.remove();
    }

    /**
     * 延遲函數
     */
    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    /**
     * 顯示測試結果
     */
    showTestResults() {
        const passCount = this.testResults.filter(
            (r) => r.status === "PASS"
        ).length;
        const failCount = this.testResults.filter(
            (r) => r.status === "FAIL"
        ).length;

        console.log("\n📊 測試結果摘要:");
        console.log(`✅ 通過: ${passCount}`);
        console.log(`❌ 失敗: ${failCount}`);
        console.log(
            `📈 成功率: ${((passCount / this.testResults.length) * 100).toFixed(
                1
            )}%`
        );

        if (failCount > 0) {
            console.log("\n❌ 失敗的測試:");
            this.testResults
                .filter((r) => r.status === "FAIL")
                .forEach((r) => {
                    console.log(`  - ${r.name}: ${r.error}`);
                });
        }

        // 顯示測試結果通知
        if (window.centerUX) {
            if (failCount === 0) {
                window.centerUX.showSuccess(
                    "測試完成",
                    `所有 ${passCount} 項測試均通過！`,
                    { duration: 5000 }
                );
            } else {
                window.centerUX.showWarning(
                    "測試完成",
                    `${passCount} 項通過，${failCount} 項失敗`,
                    { duration: 8000 }
                );
            }
        }

        console.log("\n🎉 中心管理 UX 增強功能測試完成！");
    }
}

// 自動執行測試（如果在開發環境）
if (
    window.location.hostname === "localhost" ||
    window.location.hostname.includes("dev")
) {
    $(document).ready(() => {
        // 延遲執行以確保所有腳本都已載入
        setTimeout(() => {
            new CenterUXTest();
        }, 1000);
    });
}

// 手動執行測試的全域函數
window.runCenterUXTest = () => {
    new CenterUXTest();
};
