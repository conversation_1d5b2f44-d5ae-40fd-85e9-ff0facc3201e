/**
 * 中心管理前端驗證系統
 * 提供統一的前端驗證和錯誤處理機制
 */

class CenterValidation {
    constructor() {
        this.validators = {};
        this.errorMessages = {};
        this.init();
    }

    /**
     * 初始化驗證系統
     */
    init() {
        this.setupValidators();
        this.setupErrorMessages();
        this.bindEvents();
    }

    /**
     * 設置驗證規則
     */
    setupValidators() {
        this.validators = {
            // 中心名稱驗證
            centerName: {
                required: true,
                minLength: 2,
                maxLength: 64,
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/,
                forbiddenWords: ["測試", "demo", "假", "虛擬", "臨時"],
            },

            // 中心等級驗證
            centerLevel: {
                required: true,
                type: "integer",
                min: 1,
            },

            // 狀態驗證 - 已簡化，不進行驗證
            // status: {
            //     type: "integer",
            //     values: [0, 1],
            // },

            // 會員搜尋驗證
            accountSearch: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_@.]+$/,
                dangerousPatterns: [
                    /\b(select|insert|update|delete|drop|create|alter|exec|execute)\b/i,
                    /[<>"']/,
                    /\b(script|javascript|vbscript)\b/i,
                    /\b(union|or|and)\s+\d+\s*=\s*\d+/i,
                ],
            },

            // 角色指派驗證
            roleAssignment: {
                centerId: {
                    required: true,
                    type: "integer",
                    min: 1,
                },
                accountId: {
                    required: true,
                    type: "integer",
                    min: 1,
                },
                roleCode: {
                    required: true,
                    values: [
                        "founder",
                        "director",
                        "executive_director",
                        "lecturer",
                        "market",
                        "sales",
                    ],
                },
                note: {
                    maxLength: 255,
                },
            },
        };
    }

    /**
     * 設置錯誤訊息
     */
    setupErrorMessages() {
        this.errorMessages = {
            required: "此欄位為必填",
            minLength: "至少需要 {min} 個字元",
            maxLength: "不能超過 {max} 個字元",
            pattern: "格式不正確",
            forbiddenWords: "不能包含「{word}」等詞彙",
            type: "格式無效",
            values: "請選擇有效的選項",
            min: "值不能小於 {min}",
            dangerous: "包含不安全的內容",

            // 特定欄位錯誤訊息
            centerName: {
                required: "中心名稱為必填",
                minLength: "中心名稱至少需要2個字元",
                maxLength: "中心名稱不能超過64個字元",
                pattern:
                    "中心名稱只能包含中文、英文、數字、空格、連字符、底線和括號",
                forbiddenWords: "中心名稱不能包含「{word}」等詞彙",
            },

            centerLevel: {
                required: "請選擇中心等級",
            },

            accountSearch: {
                required: "搜尋關鍵字為必填",
                minLength: "搜尋關鍵字至少需要2個字元",
                maxLength: "搜尋關鍵字不能超過50個字元",
                pattern: "搜尋關鍵字包含無效字元",
                dangerous: "搜尋關鍵字包含不安全的內容",
            },

            roleAssignment: {
                centerId: "中心ID無效",
                accountId: "請選擇有效的會員",
                roleCode: "請選擇有效的角色",
                note: "備註不能超過255個字元",
            },
        };
    }

    /**
     * 綁定事件
     */
    bindEvents() {
        // 即時驗證事件
        $(document).on("blur", "[data-validate]", (e) => {
            this.validateField($(e.target));
        });

        $(document).on("change", "[data-validate]", (e) => {
            this.validateField($(e.target));
        });

        // 表單提交驗證
        $(document).on("submit", "[data-validate-form]", (e) => {
            if (!this.validateForm($(e.target))) {
                e.preventDefault();
                return false;
            }
        });

        // 清除錯誤事件
        $(document).on("input", "[data-validate]", (e) => {
            this.clearFieldError($(e.target));
        });
    }

    /**
     * 驗證單個欄位
     */
    validateField($field) {
        const fieldName = $field.data("validate");
        const value = $field.val();
        const validator = this.getValidator(fieldName);

        if (!validator) {
            console.warn("找不到驗證規則:", fieldName);
            return true;
        }

        const result = this.runValidation(value, validator, fieldName);

        if (result.isValid) {
            this.clearFieldError($field);
            return true;
        } else {
            this.showFieldError($field, result.message);
            return false;
        }
    }

    /**
     * 驗證整個表單
     */
    validateForm($form) {
        let isValid = true;
        const $fields = $form.find("[data-validate]");

        $fields.each((index, field) => {
            const $field = $(field);
            if (!this.validateField($field)) {
                isValid = false;
            }
        });

        // 顯示表單級別的錯誤摘要
        if (!isValid) {
            this.showFormErrorSummary($form);
        } else {
            this.clearFormErrorSummary($form);
        }

        return isValid;
    }

    /**
     * 執行驗證邏輯
     */
    runValidation(value, validator, fieldName) {
        const result = {
            isValid: true,
            message: "",
        };

        // 必填驗證
        if (validator.required && (!value || value.trim() === "")) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "required");
            return result;
        }

        // 如果值為空且不是必填，跳過其他驗證
        if (!value || value.trim() === "") {
            return result;
        }

        // 長度驗證
        if (validator.minLength && value.length < validator.minLength) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "minLength", {
                min: validator.minLength,
            });
            return result;
        }

        if (validator.maxLength && value.length > validator.maxLength) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "maxLength", {
                max: validator.maxLength,
            });
            return result;
        }

        // 格式驗證
        if (validator.pattern && !validator.pattern.test(value)) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "pattern");
            return result;
        }

        // 類型驗證
        if (validator.type) {
            if (!this.validateType(value, validator.type)) {
                result.isValid = false;
                result.message = this.getErrorMessage(fieldName, "type");
                return result;
            }
        }

        // 值範圍驗證
        if (validator.values && !validator.values.includes(value)) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "values");
            return result;
        }

        // 最小值驗證
        if (validator.min !== undefined && parseFloat(value) < validator.min) {
            result.isValid = false;
            result.message = this.getErrorMessage(fieldName, "min", {
                min: validator.min,
            });
            return result;
        }

        // 禁用詞彙驗證（檢查是否以禁用詞彙開頭）
        if (validator.forbiddenWords) {
            for (const word of validator.forbiddenWords) {
                if (value.toLowerCase().startsWith(word.toLowerCase())) {
                    result.isValid = false;
                    result.message = this.getErrorMessage(
                        fieldName,
                        "forbiddenWords",
                        { word: word }
                    );
                    return result;
                }
            }
        }

        // 危險模式驗證
        if (validator.dangerousPatterns) {
            for (const pattern of validator.dangerousPatterns) {
                if (pattern.test(value)) {
                    result.isValid = false;
                    result.message = this.getErrorMessage(
                        fieldName,
                        "dangerous"
                    );
                    return result;
                }
            }
        }

        return result;
    }

    /**
     * 類型驗證
     */
    validateType(value, type) {
        switch (type) {
            case "integer":
                return /^\d+$/.test(value) && !isNaN(parseInt(value));
            case "float":
                return !isNaN(parseFloat(value));
            case "email":
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
            case "url":
                try {
                    new URL(value);
                    return true;
                } catch {
                    return false;
                }
            default:
                return true;
        }
    }

    /**
     * 獲取驗證器
     */
    getValidator(fieldName) {
        // 支援嵌套的驗證器配置
        const parts = fieldName.split(".");
        let validator = this.validators;

        for (const part of parts) {
            if (validator[part]) {
                validator = validator[part];
            } else {
                return null;
            }
        }

        return validator;
    }

    /**
     * 獲取錯誤訊息
     */
    getErrorMessage(fieldName, errorType, params = {}) {
        // 嘗試獲取特定欄位的錯誤訊息
        const fieldMessages = this.errorMessages[fieldName];
        if (fieldMessages && fieldMessages[errorType]) {
            return this.formatMessage(fieldMessages[errorType], params);
        }

        // 使用通用錯誤訊息
        const genericMessage = this.errorMessages[errorType];
        if (genericMessage) {
            return this.formatMessage(genericMessage, params);
        }

        return "驗證失敗";
    }

    /**
     * 格式化訊息
     */
    formatMessage(message, params) {
        let formatted = message;
        for (const [key, value] of Object.entries(params)) {
            formatted = formatted.replace(
                new RegExp(`\\{${key}\\}`, "g"),
                value
            );
        }
        return formatted;
    }

    /**
     * 顯示欄位錯誤
     */
    showFieldError($field, message) {
        const fieldId = $field.attr("id") || $field.attr("name");
        const $errorDiv = $(`#${fieldId}-error`);

        // 添加錯誤樣式
        $field.addClass("is-invalid");

        // 顯示錯誤訊息
        if ($errorDiv.length > 0) {
            $errorDiv.text(message).show();
        } else {
            // 如果沒有專用的錯誤容器，創建一個
            const errorHtml = `<div class="invalid-feedback" id="${fieldId}-error">${message}</div>`;
            $field.after(errorHtml);
        }

        // 添加錯誤圖標
        this.addErrorIcon($field);
    }

    /**
     * 清除欄位錯誤
     */
    clearFieldError($field) {
        const fieldId = $field.attr("id") || $field.attr("name");
        const $errorDiv = $(`#${fieldId}-error`);

        // 移除錯誤樣式
        $field.removeClass("is-invalid");

        // 隱藏錯誤訊息
        $errorDiv.hide().text("");

        // 移除錯誤圖標
        this.removeErrorIcon($field);
    }

    /**
     * 添加錯誤圖標
     */
    addErrorIcon($field) {
        const $container = $field.closest(".form-group, .input-group");
        if ($container.find(".field-error-icon").length === 0) {
            const iconHtml =
                '<i class="bi bi-exclamation-circle text-danger field-error-icon" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); z-index: 10;"></i>';
            $container.css("position", "relative").append(iconHtml);
        }
    }

    /**
     * 移除錯誤圖標
     */
    removeErrorIcon($field) {
        const $container = $field.closest(".form-group, .input-group");
        $container.find(".field-error-icon").remove();
    }

    /**
     * 顯示表單錯誤摘要
     */
    showFormErrorSummary($form) {
        const $errors = $form.find(".is-invalid");
        if ($errors.length === 0) return;

        let $summary = $form.find(".form-error-summary");
        if ($summary.length === 0) {
            $summary = $(
                '<div class="alert alert-danger form-error-summary"><h6>請修正以下錯誤：</h6><ul></ul></div>'
            );
            $form.prepend($summary);
        }

        const $list = $summary.find("ul").empty();
        $errors.each((index, field) => {
            const $field = $(field);
            const fieldId = $field.attr("id") || $field.attr("name");
            const $errorDiv = $(`#${fieldId}-error`);
            const errorMessage = $errorDiv.text();
            const fieldLabel =
                $field.closest(".form-group").find("label").text() || fieldId;

            if (errorMessage) {
                $list.append(
                    `<li><strong>${fieldLabel}:</strong> ${errorMessage}</li>`
                );
            }
        });

        $summary.show();
    }

    /**
     * 清除表單錯誤摘要
     */
    clearFormErrorSummary($form) {
        $form.find(".form-error-summary").hide();
    }

    /**
     * 驗證中心名稱（特殊方法）
     */
    validateCenterName(name) {
        return this.runValidation(
            name,
            this.validators.centerName,
            "centerName"
        );
    }

    /**
     * 驗證會員搜尋（特殊方法）
     */
    validateAccountSearch(query) {
        return this.runValidation(
            query,
            this.validators.accountSearch,
            "accountSearch"
        );
    }

    /**
     * 驗證角色指派（特殊方法）
     */
    validateRoleAssignment(data) {
        const results = {};
        const validator = this.validators.roleAssignment;

        for (const [field, value] of Object.entries(data)) {
            if (validator[field]) {
                results[field] = this.runValidation(
                    value,
                    validator[field],
                    `roleAssignment.${field}`
                );
            }
        }

        const isValid = Object.values(results).every(
            (result) => result.isValid
        );
        return {
            isValid,
            results,
            firstError:
                Object.values(results).find((result) => !result.isValid)
                    ?.message || "",
        };
    }

    /**
     * 批量驗證
     */
    validateBatch(data, validatorName) {
        const validator = this.getValidator(validatorName);
        if (!validator) {
            return { isValid: false, message: "找不到驗證規則" };
        }

        const results = {};
        for (const [field, value] of Object.entries(data)) {
            if (validator[field]) {
                results[field] = this.runValidation(
                    value,
                    validator[field],
                    `${validatorName}.${field}`
                );
            }
        }

        const isValid = Object.values(results).every(
            (result) => result.isValid
        );
        return {
            isValid,
            results,
            errors: Object.entries(results)
                .filter(([field, result]) => !result.isValid)
                .reduce((acc, [field, result]) => {
                    acc[field] = result.message;
                    return acc;
                }, {}),
        };
    }

    /**
     * 重置表單驗證狀態
     */
    resetForm($form) {
        $form.find(".is-invalid").removeClass("is-invalid");
        $form.find(".invalid-feedback").hide().text("");
        $form.find(".field-error-icon").remove();
        $form.find(".form-error-summary").hide();
    }

    /**
     * 設置自定義驗證器
     */
    addValidator(name, validator) {
        this.validators[name] = validator;
    }

    /**
     * 設置自定義錯誤訊息
     */
    addErrorMessages(name, messages) {
        this.errorMessages[name] = messages;
    }
}

// 全域實例
window.centerValidation = new CenterValidation();

// 整合 UX 增強功能
if (window.centerUX) {
    // 擴展驗證系統以支援 UX 增強
    const originalShowFieldError = CenterValidation.prototype.showFieldError;
    const originalClearFieldError = CenterValidation.prototype.clearFieldError;
    const originalValidateForm = CenterValidation.prototype.validateForm;

    // 增強錯誤顯示
    CenterValidation.prototype.showFieldError = function ($field, message) {
        originalShowFieldError.call(this, $field, message);

        // 添加震動效果
        $field.addClass("shake-animation");
        setTimeout(() => {
            $field.removeClass("shake-animation");
        }, 500);

        // 顯示錯誤通知（如果是重要欄位）
        if ($field.attr("required")) {
            window.centerUX.showError("驗證錯誤", message, {
                duration: 3000,
            });
        }
    };

    // 增強錯誤清除
    CenterValidation.prototype.clearFieldError = function ($field) {
        originalClearFieldError.call(this, $field);

        // 添加成功效果
        if ($field.val() && $field.val().trim() !== "") {
            $field.addClass("success-animation");
            setTimeout(() => {
                $field.removeClass("success-animation");
            }, 500);
        }
    };

    // 增強表單驗證
    CenterValidation.prototype.validateForm = function ($form) {
        // 顯示驗證載入狀態
        window.centerUX.showElementLoading($form);

        const result = originalValidateForm.call(this, $form);

        // 隱藏載入狀態
        setTimeout(() => {
            window.centerUX.hideElementLoading($form);
        }, 300);

        if (!result) {
            // 顯示驗證失敗通知
            const errorCount = $form.find(".is-invalid").length;
            window.centerUX.showError(
                "表單驗證失敗",
                `發現 ${errorCount} 個錯誤，請檢查並修正`,
                { duration: 5000 }
            );

            // 滾動到第一個錯誤
            const $firstError = $form.find(".is-invalid").first();
            if ($firstError.length > 0) {
                $("html, body").animate(
                    {
                        scrollTop: $firstError.offset().top - 100,
                    },
                    500
                );
            }
        } else {
            // 顯示驗證成功通知
            window.centerUX.showSuccess("驗證通過", "表單資料驗證成功", {
                duration: 2000,
            });
        }

        return result;
    };
}

// jQuery 插件
$.fn.validateCenter = function (options = {}) {
    return this.each(function () {
        const $form = $(this);

        // 設置表單驗證屬性
        $form.attr("data-validate-form", "true");

        // 為表單欄位添加驗證屬性
        if (options.fields) {
            Object.entries(options.fields).forEach(([selector, validator]) => {
                $form.find(selector).attr("data-validate", validator);
            });
        }

        // 綁定提交事件
        $form.on("submit", function (e) {
            if (!window.centerValidation.validateForm($form)) {
                e.preventDefault();
                return false;
            }
        });
    });
};

// 使用範例：
// $('#centerForm').validateCenter({
//     fields: {
//         '#name': 'centerName',
//         '#center_level_id': 'centerLevel',
//         '#status': 'status'
//     }
// });
